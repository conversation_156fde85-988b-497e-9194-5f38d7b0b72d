<!-- Start Of Header -->
<div class="rowline mb-0" *ngIf="!isLoading">
  <div class="page-title-box ">
      <div class="d-flex k-gap-2 align-items-center ">
          <button class="actions-icons action-back btn text-white" (click)="back()">
              <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
          </button>
          <h4 class="page-title fw-4 ps-2 truncate" *ngIf="!isAllValidated">
            <span class="d-none d-md-inline">Répartition des produits Commande N° {{enteteBlConsolide?.codeCommande}}</span>
            <span class="d-md-none">R<PERSON>parti {{enteteBlConsolide?.codeCommande}}</span>
          </h4>
          <h4 class="page-title fw-4 ps-2" *ngIf="isAllValidated">
            <span class="d-none d-md-inline">Dispatch du BL N°{{enteteBlConsolide?.numeroBl}}</span>
            <span class="d-md-none">Dispatch de BL-{{enteteBlConsolide?.numeroBl}}</span>
          </h4>
      </div>

      <div class="d-none d-lg-flex px-1 mr-2">
          <div class="row justify-content-end align-items-center">
              <button *ngIf="!isAllValidated"  type="button" class="btn btn-sm btn-primary m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="saveBl()" style="padding-block: 6px;" >
                  <i class="bi bi-bookmark-check-fill"></i>
                  <span class="d-none d-md-inline">Enregistrer</span>
              </button>

              <button *ngIf="!isAllValidated" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="validateDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-check"></i>
                <span class="d-none d-md-inline">Valider</span>
              </button>

              <button *ngIf="isAllValidated && enteteBlConsolide?.etatBl === 'REPARTI'" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="showmodalByrouterQueryParam()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Bons de sortie</span>
              </button>

              <button  type="button" class="btn btn-sm btn-info m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="openExchangeModal()"  style="padding-block: 6px;" >
                <i class="bi bi-arrow-left-right"></i>
                <span class="d-none d-md-inline">Échange</span>
              </button>

              <button *ngIf=" enteteBlConsolide?.etatBl !== 'ANNULE'" type="button" class="btn btn-sm btn-danger m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="deleteDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Supprimer</span>
              </button>

              <button (click)="back()" type="button" style="padding-block: 6px;" class="btn btn-sm btn-dark text-white m-1">
                <i class="mdi mdi-close"></i> Quitter
              </button>
          </div>
      </div>
  </div>
</div>
<!-- END HEADER -->

<div class="row mx-2" *ngIf="!isLoading">
<div class="card bg-transparent my-1 w-100">
    <form  class="p-0 m-0" autocomplete="off">
       <ng-container [ngTemplateOutlet]="BLPage"></ng-container>
    </form>
 </div>
</div>

<ng-template  #BLPage>
<div class="card">
  <div class="card">
    <div class="card-body p-1">
      <form class="p-0 m-0" autocomplete="off" [formGroup]="saisieBLForm">
        <div class="px-1 bg-white mb-sm-0">
          <div class="row divider-y" wphFocusTrap>
            <div class="col-md-4">
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Distributeur <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale" wphAutoFocus class="form-control pl-4 "
                      placeholder="Entrez Le Distribiteur" formControlName="fournisseur" />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-truck"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Commande <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale"
                        class="form-control pl-4"
                      formControlName="enteteCommandeAchatGroupe" placeholder="veuillez donner le code commande" #commandeInput />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-receipt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="row">
                <div class="col-12  my-1">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="number" id="raisonSociale"
                            formControlName="montantSaisi" class="form-control pl-4"
                            placeholder="Entrez Montant BL" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-cash"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Remise <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="tauxRf" placeholder="Entrez Remise"
                            />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-gift"></i>
                          </div>
                          <div class="picker-icons picker-icons-end" style="right: 2px;">
                            <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL Calculé <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="cumulBl" placeholder="Entrez Cumul BL" [readOnly]="true" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-search text-dark"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="transporteur" class="form-label p-0 col-12">Transporteur</label>
                    <div class="input-group picker-input">
                      <input type="text" id="transporteur" class="form-control pl-4"
                        formControlName="transporteur" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">N° BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale" class="form-control pl-4" formControlName="numeroBl"
                        placeholder="Entrez N° BL" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Date De BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input type="text" [readOnly]="true" class="form-control form-control-md"
                        formControlName="dateReceptionBl" id="dateDebut" [disabled]="true">
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="card-footer p-0">
    <div class="card bg-transparent my-1 w-100 text-dark rounded">
      <div class="card-body bg-white px-1">
        <div *ngIf="notDispatched &&  !isLoading">
          <div class="card">
            <div class="card-body text-center">
              <h2>Choisissez la méthode de répartition des produits</h2>
              <p class="font-18">Sélectionnez comment vous souhaitez répartir les produits entre les membres du groupe :</p>
              <div class="d-flex align-items-center justify-content-center k-gap-2">
                <button class="btn btn-sm btn-success m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('P')">
                  <span>Donner le reste à ce qui est le plus commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus grande quantité commandée."></i>
                </button>
                <button class="btn btn-sm btn-outline-dark m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('S')">
                  <span>Donner le reste à ce qui est le moins commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus petite quantité commandée."></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <kendo-grid [data]="gridData" *ngIf="!notDispatched" wphScrollCheck   wphFocusTrapPrefixed prefix="qlivree-"  [rowClass]="rowClass"   class="fs-grid fs-grid-white bl-grid dispatch-grid" style="min-height: 400px;max-height: calc(100vh - 400px)">
          <kendo-grid-column-group title="Liste des produits" headerClass="end-phase">
            <kendo-grid-column field="designation" title="Designation Produit" [width]="300"  class="text-wrap">
              <ng-template kendoGridCellTemplate let-dataItem>
               <div class="d-flex align-items-center k-gap-1" >
                {{dataItem.designation}}  <span *ngIf="dataItem.isCadeau" class="text-success font-weight-bold">(Offert)</span>
                <span *ngIf="dataItem?.isvalidated === undefined; else inValidTemplate" class="text-success font-weight-bold ml-auto">
                  <i class="bi bi-check-circle-fill" style="color: #007514; font-size: 20px; line-height: 1;"></i>
                </span>
                <ng-template #inValidTemplate>
                  <span class="text-danger font-weight-bold ml-auto" ngbTooltip="{{dataItem.raison}}" [placement]="'bottom'">
                    <i class="bi bi-question-circle-fill" style="color: #D92D20; font-size: 20px; line-height: 1;"

                    ></i>
                  </span>
                </ng-template>
               </div>
              </ng-template>
              <ng-template kendoGridFooterTemplate let-column>
                <span class="text-success font-weight-bold">Total:</span>
               </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteCommandee" title="Qté Cmd" [width]="80" class="text-wrap">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteCommandee']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [hidden]="!isCoffretEnabled" field="qteFixePrdInCoffret" class="text-center" title="Qté Fixe" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.qteFixePrdInCoffret | number: '1.0-0' }}
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['qteFixePrdInCoffret']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteLivree" title="Qté Livré" [width]="100">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteLivree']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteUg" title="Ug Livré" [width]="100" class="end-phase" footerClass="end-phase" headerClass="end-phase">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteUg']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
           </kendo-grid-column-group>

            <kendo-grid-column-group *ngFor="let member of listMembers;let index = index" [title]="isCoffretEnabled ? member.nom + ' (' + memeberSummary.get(member.id)?.coffretCommandee + ' Coffrets)': member.nom" class="member-block" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase-member  font-weight-bold':true}">

              <kendo-grid-column field="drioich_cmd" title="Cmd" [width]="100" class="text-center" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem >
                    {{getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id)?.quantiteCommandee}}
                  </ng-template>
                  <ng-template kendoGridFooterTemplate let-column>
                    <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteCommandee}}</span>
                   </ng-template>
               </kendo-grid-column>
              <kendo-grid-column field="drioich_livre" title="Q Livré" [width]="100" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                    <input type="text" class="form-control text-center font-weight-bold text-dark"
                    [(ngModel)]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteLivree"
                    [readOnly]="isAllValidated"  [attr.data-prefix]="'qliv'+member.id+'ree-'" id="qliv{{member.id}}ree-{{rowIndex}}"
                    (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteLivree',$event)"
                    wphAllowOnlyNumbers [maxValue]="99999999"
                    [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteLivree') ? '#ccc' : 'inherit'}"
                    >
                  </div>
                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteLivree}}</span>
                 </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="drioich_cmd" title="UG Livré" [width]="100" class="text-center end-phase-member" footerClass="end-phase" headerClass="end-phase-member" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase':index +1 !== listMembers.size}">

                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                  <input type="text" class="form-control text-center font-weight-bold text-dark"
                  [value]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteUg"
                   [readOnly]="isAllValidated"
                   [attr.data-prefix]="'qug'+member.id" id="qu{{member.id}}g-{{rowIndex}}"
                  (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteUg',$event)"
                  wphAllowOnlyNumbers [maxValue]="99999999"
                  [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteUg') ? '#ccc' : 'inherit'}"
                  >
                </div>

                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteUg}}</span>
                 </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>
</ng-template>




<ng-template #ChoseBLUnitaire let-modal>
  <div class="modal-header">
      <h4 class="modal-title text-dark" id="modal-basic-title">{{ 'Consulter les bons de sortie' | uppercase }}</h4>
      <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <i class="mdi mdi-close"></i>
      </button>
  </div>

  <div class="modal-body">

    <div class="d-flex  py-1 justify-content-end align-items-center">
      <button type="button" class="btn btn-sm btn-success m-1 font-16" (click)="sendBLSUnitaire()"  style="padding-block: 6px; border-radius: 8px;" >
        <i class="bi bi-send"></i>
        Envoyes Les Bons de sortie
      </button>
    </div>
    <kendo-grid [data]="enteteBlUnitaire"   [selectable]="{mode: 'multiple'}"
    [(selectedKeys)]="selectedKeys"
    kendoGridSelectBy="id"
    (cellClick)="OnSelectionChange($event)"
    class="fs-grid fs-grid-white"
    style="min-height: 300px;"

    >
      <kendo-grid-column field="numeroBl" title="N° BL" [width]="80">
      </kendo-grid-column>

      <kendo-grid-column field="codeCommande" title="N° Cmd" [width]="100">
      </kendo-grid-column>

      <kendo-grid-column field="dateCommande" title="Date Cmd" [width]="150">

        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCommande | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Raison Sociale" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
         PH. {{dataItem.enteteCommandeAchatGroupe.client.raisonSociale}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Nom Pharmacien" [width]="150" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
        Dr. {{dataItem.enteteCommandeAchatGroupe.client.nomResponsable}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateReceptionBl" title="Date BL" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateReceptionBl | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateCreation" title="Crée le" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="fournisseur.raisonSociale" title="Distributeur" [width]="120"></kendo-grid-column>

      <kendo-grid-column field="montantSaisi" title="Montant BL" [width]="120" class="text-right">
      </kendo-grid-column>


      <kendo-grid-column field="etatBl" title="Statut" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-element-status
            [state]="(dataItem?.etat === 'cloturee') ? 'C' : dataItem.etatBl === 'ANNULE' ? 'BANNULE' : dataItem.etatBl === 'VALIDE' ? 'BVALIDE' : dataItem.etatBl"></app-element-status>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

  </div>
</ng-template>






<ng-template #ModalShowMoreMembers let-modal>
  <div class="modal-header">
    <h4 class="modal-title text-dark" id="modal-basic-title">Liste des membres</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <kendo-grid [data]="membersHasNoEmail" wphScrollCheck
       class="fs-grid fs-grid-white"
    style="max-height: 300px; min-height: 200px;"
    >
      <kendo-grid-column field="nom" title="Nom" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
           DR. {{dataItem.nom}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="raisonSociale" title="Raison Sociale" class="text-wrap" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          PH. {{dataItem.raisonSociale}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="email" title="Email" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="dataItem.email; else: emptyEmail">
            <span *ngIf="dataItem.email.length > 0; else: emptyEmail">{{dataItem.email}}</span>
          </span>
          <ng-template #emptyEmail>
            <span class="text-warning d-flex align-items-center k-gap-1">
              <i class="bi bi-exclamation-triangle" style="line-height: 1;"></i>
              indisponible
            </span>
          </ng-template>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</ng-template>


<!-- Exchange Modal -->
<ng-template #ExchangeModal let-modal>
  <div class="modal-header border-0 pb-0">
    <div class="d-flex align-items-center">
      <div class="modal-icon me-3">
        <i class="bi bi-arrow-left-right text-primary" style="font-size: 24px;"></i>
      </div>
      <div>
        <h4 class="modal-title text-dark mb-1">Échange entre membres</h4>
        <p class="text-muted mb-0 small">Gérez les échanges de produits entre les membres du groupe</p>
      </div>
    </div>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss('Cross click')"></button>
  </div>

  <div class="modal-body px-4">
    <!-- Progress Indicator -->
    <div class="progress-indicator mb-4" *ngIf="pendingExchanges.length > 0">
      <div class="d-flex align-items-center justify-content-between">
        <span class="text-muted small">Échanges en cours</span>
        <span class="badge bg-primary rounded-pill">{{pendingExchanges.length}}</span>
      </div>
      <div class="progress mt-2" style="height: 4px;">
        <div class="progress-bar bg-primary" role="progressbar" [style.width.%]="(pendingExchanges.length / listMembers.size) * 100"></div>
      </div>
    </div>

    <form [formGroup]="exchangeForm" class="exchange-form">
      <!-- Member Selection Cards -->
      <div class="row g-3 mb-4">
        <div class="col-md-6">
          <div class="member-selection-card">
            <div class="card-header">
              <i class="bi bi-person-dash text-warning me-2"></i>
              <span class="fw-medium">Membre donneur</span>
              <span class="text-danger ms-1">*</span>
            </div>
            <div class="card-body">
              <select class="form-select modern-select" formControlName="clientDonneur" (change)="onDonneurChange()">
                <option value="">Choisir le membre donneur</option>
                <option *ngFor="let member of listMembers" [value]="member.id">
                  <i class="bi bi-person-circle"></i> Dr. {{member.nom}}
                </option>
              </select>
              <div class="member-info mt-2" *ngIf="selectedDonneurId">
                <small class="text-muted">
                  <i class="bi bi-info-circle me-1"></i>
                  Quantités disponibles pour l'échange
                </small>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="member-selection-card">
            <div class="card-header">
              <i class="bi bi-person-plus text-success me-2"></i>
              <span class="fw-medium">Membre receveur</span>
              <span class="text-danger ms-1">*</span>
            </div>
            <div class="card-body">
              <select class="form-select modern-select" formControlName="clientReceveur" (change)="onReceveurChange()">
                <option value="">Choisir le membre receveur</option>
                <option *ngFor="let member of listMembers" [value]="member.id" [disabled]="member.id === exchangeForm.get('clientDonneur')?.value">
                  <i class="bi bi-person-circle"></i> Dr. {{member.nom}}
                </option>
              </select>
              <div class="member-info mt-2" *ngIf="selectedReceveurId">
                <small class="text-muted">
                  <i class="bi bi-info-circle me-1"></i>
                  Quantités actuelles du membre
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Products Exchange Section -->
      <div class="products-exchange-section" *ngIf="exchangeForm.get('clientDonneur')?.value && exchangeForm.get('clientReceveur')?.value">
        <div class="section-header mb-3">
          <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
              <i class="bi bi-box-seam text-primary me-2" style="font-size: 20px;"></i>
              <h5 class="mb-0 fw-medium">Produits disponibles pour l'échange</h5>
            </div>
            <div class="exchange-summary">
              <span class="badge bg-light text-dark">
                {{getValidExchangeProductsCount()}} produit(s) disponible(s)
              </span>
            </div>
          </div>
          <p class="text-muted small mb-0 mt-1">Sélectionnez les quantités à échanger entre les membres</p>
        </div>

        <!-- Modern Products Grid -->
        <div class="products-grid-container">
          <div class="table-responsive">
            <table class="table table-hover modern-table">
              <thead class="table-light">
                <tr>
                  <th class="border-0">
                    <div class="d-flex align-items-center">
                      <i class="bi bi-box me-2 text-muted"></i>
                      Produit
                    </div>
                  </th>
                  <th class="border-0 text-center">
                    <div class="d-flex align-items-center justify-content-center">
                      <i class="bi bi-person-dash me-2 text-warning"></i>
                      Qté Donneur
                    </div>
                  </th>
                  <th class="border-0 text-center">
                    <div class="d-flex align-items-center justify-content-center">
                      <i class="bi bi-person-plus me-2 text-success"></i>
                      Qté Receveur
                    </div>
                  </th>
                  <th class="border-0 text-center">
                    <div class="d-flex align-items-center justify-content-center">
                      <i class="bi bi-arrow-left-right me-2 text-primary"></i>
                      Qté à échanger
                    </div>
                  </th>
                  <th class="border-0 text-center">
                    <div class="d-flex align-items-center justify-content-center">
                      <i class="bi bi-calculator me-2 text-info"></i>
                      Disponible après
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let product of exchangeProductsData; let i = index"
                    class="product-row"
                    [class.table-warning]="product.quantiteEchange > 0"
                    [class.table-danger]="hasQuantityError(product)">
                  <td class="align-middle">
                    <div class="product-info">
                      <div class="fw-medium text-dark">{{product.designation}}</div>
                      <small class="text-muted">{{product.produitDto?.codeProduit || 'N/A'}}</small>
                    </div>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge bg-warning bg-opacity-10 text-warning px-3 py-2">
                      {{product.quantiteDonneurDisponible}}
                    </span>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge bg-success bg-opacity-10 text-success px-3 py-2">
                      {{product.quantiteReceveurActuelle}}
                    </span>
                  </td>
                  <td class="align-middle">
                    <div class="quantity-input-container">
                      <div class="input-group input-group-sm">
                        <button class="btn btn-outline-secondary" type="button"
                                (click)="decrementQuantity(product)"
                                [disabled]="product.quantiteEchange <= 0">
                          <i class="bi bi-dash"></i>
                        </button>
                        <input type="number"
                               class="form-control text-center fw-medium quantity-input"
                               [(ngModel)]="product.quantiteEchange"
                               [ngModelOptions]="{standalone: true}"
                               [max]="getMaxAvailableQuantity(product)"
                               min="0"
                               (input)="onQuantiteEchangeChange(product, $event)"
                               [class.is-invalid]="hasQuantityError(product)"
                               placeholder="0">
                        <button class="btn btn-outline-secondary" type="button"
                                (click)="incrementQuantity(product)"
                                [disabled]="product.quantiteEchange >= getMaxAvailableQuantity(product)">
                          <i class="bi bi-plus"></i>
                        </button>
                      </div>
                      <div class="invalid-feedback" *ngIf="hasQuantityError(product)">
                        {{getQuantityErrorMessage(product)}}
                      </div>
                    </div>
                  </td>
                  <td class="align-middle text-center">
                    <span class="badge bg-info bg-opacity-10 text-info px-3 py-2">
                      {{getAvailableAfterExchange(product)}}
                    </span>
                  </td>
                </tr>
                <tr *ngIf="exchangeProductsData.length === 0">
                  <td colspan="5" class="text-center py-4 text-muted">
                    <i class="bi bi-inbox display-6 d-block mb-2 opacity-50"></i>
                    Aucun produit disponible pour l'échange
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Pending Exchanges Summary -->
      <div class="pending-exchanges-section mt-4" *ngIf="pendingExchanges.length > 0">
        <div class="card border-0 bg-light">
          <div class="card-header bg-transparent border-0 pb-2">
            <div class="d-flex align-items-center">
              <i class="bi bi-clock-history text-primary me-2"></i>
              <h6 class="mb-0 fw-medium">Échanges en attente</h6>
              <span class="badge bg-primary ms-2">{{pendingExchanges.length}}</span>
            </div>
          </div>
          <div class="card-body pt-0">
            <div class="exchange-list">
              <div *ngFor="let exchange of pendingExchanges; let i = index"
                   class="exchange-item d-flex align-items-center justify-content-between p-3 mb-2 bg-white rounded border">
                <div class="exchange-info">
                  <div class="d-flex align-items-center mb-1">
                    <div class="member-avatar bg-warning bg-opacity-10 text-warning rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                      <i class="bi bi-person-dash small"></i>
                    </div>
                    <span class="fw-medium">Dr. {{exchange.clientDonneur.nomResponsable}}</span>
                    <i class="bi bi-arrow-right mx-2 text-muted"></i>
                    <div class="member-avatar bg-success bg-opacity-10 text-success rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                      <i class="bi bi-person-plus small"></i>
                    </div>
                    <span class="fw-medium">Dr. {{exchange.clientReceveur.nomResponsable}}</span>
                  </div>
                  <small class="text-muted">
                    <i class="bi bi-box me-1"></i>
                    {{exchange.lignes.length}} produit(s) • {{getTotalExchangeQuantity(exchange)}} unité(s)
                  </small>
                </div>
                <button class="btn btn-sm btn-outline-danger" (click)="removePendingExchange(i)" title="Supprimer cet échange">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="modal-actions mt-4 pt-3 border-top">
        <div class="d-flex justify-content-between align-items-center">
          <button type="button" class="btn btn-light" (click)="modal.dismiss('cancel')">
            <i class="bi bi-x-circle me-2"></i>Annuler
          </button>

          <div class="d-flex gap-2">
            <button type="button"
                    class="btn btn-primary"
                    (click)="processExchange(modal)"
                    [disabled]="!isExchangeValid()">
              <i class="bi bi-plus-circle me-2"></i>
              Ajouter cet échange
            </button>
            <button type="button"
                    class="btn btn-success"
                    (click)="modal.close('all-exchanges-completed')"
                    [disabled]="pendingExchanges.length === 0">
              <i class="bi bi-check-circle me-2"></i>
              Terminer ({{pendingExchanges.length}})
            </button>
          </div>
        </div>

        <!-- Exchange Summary -->
        <div class="exchange-summary-footer mt-3" *ngIf="pendingExchanges.length > 0">
          <div class="row text-center">
            <div class="col-4">
              <div class="summary-stat">
                <div class="h5 mb-0 text-primary">{{pendingExchanges.length}}</div>
                <small class="text-muted">Échange(s)</small>
              </div>
            </div>
            <div class="col-4">
              <div class="summary-stat">
                <div class="h5 mb-0 text-success">{{getTotalProductsInExchanges()}}</div>
                <small class="text-muted">Produit(s)</small>
              </div>
            </div>
            <div class="col-4">
              <div class="summary-stat">
                <div class="h5 mb-0 text-info">{{getTotalQuantityInExchanges()}}</div>
                <small class="text-muted">Unité(s)</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</ng-template>
