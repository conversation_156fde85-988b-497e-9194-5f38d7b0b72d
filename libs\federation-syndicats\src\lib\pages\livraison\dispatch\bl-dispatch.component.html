<!-- Start Of Header -->
<div class="rowline mb-0" *ngIf="!isLoading">
  <div class="page-title-box ">
      <div class="d-flex k-gap-2 align-items-center ">
          <button class="actions-icons action-back btn text-white" (click)="back()">
              <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
          </button>
          <h4 class="page-title fw-4 ps-2 truncate" *ngIf="!isAllValidated">
            <span class="d-none d-md-inline">Répartition des produits Commande N° {{enteteBlConsolide?.codeCommande}}</span>
            <span class="d-md-none">R<PERSON>parti {{enteteBlConsolide?.codeCommande}}</span>
          </h4>
          <h4 class="page-title fw-4 ps-2" *ngIf="isAllValidated">
            <span class="d-none d-md-inline">Dispatch du BL N°{{enteteBlConsolide?.numeroBl}}</span>
            <span class="d-md-none">Dispatch de BL-{{enteteBlConsolide?.numeroBl}}</span>
          </h4>
      </div>

      <div class="d-none d-lg-flex px-1 mr-2">
          <div class="row justify-content-end align-items-center">
              <button *ngIf="!isAllValidated"  type="button" class="btn btn-sm btn-primary m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="saveBl()" style="padding-block: 6px;" >
                  <i class="bi bi-bookmark-check-fill"></i>
                  <span class="d-none d-md-inline">Enregistrer</span>
              </button>

              <button *ngIf="!isAllValidated" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="validateDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-check"></i>
                <span class="d-none d-md-inline">Valider</span>
              </button>

              <button *ngIf="isAllValidated && enteteBlConsolide?.etatBl === 'REPARTI'" type="button" class="btn btn-sm btn-success m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="showmodalByrouterQueryParam()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Bons de sortie</span>
              </button>

              <button *ngIf=" enteteBlConsolide?.etatBl !== 'ANNULE'" type="button" class="btn btn-sm btn-danger m-1  d-flex align-items-center justify-content-center k-gap-1" (click)="deleteDispatch()"  style="padding-block: 6px;" >
                <i class="bi bi-file-earmark-text"></i>
                <span class="d-none d-md-inline">Supprimer</span>
              </button>

              <button (click)="back()" type="button" style="padding-block: 6px;" class="btn btn-sm btn-dark text-white m-1">
                <i class="mdi mdi-close"></i> Quitter
              </button>
          </div>
      </div>
  </div>
</div>
<!-- END HEADER -->

<div class="row mx-2" *ngIf="!isLoading">
<div class="card bg-transparent my-1 w-100">
    <form  class="p-0 m-0" autocomplete="off">
       <ng-container [ngTemplateOutlet]="BLPage"></ng-container>
    </form>
 </div>
</div>

<ng-template  #BLPage>
<div class="card">
  <div class="card">
    <div class="card-body p-1">
      <form class="p-0 m-0" autocomplete="off" [formGroup]="saisieBLForm">
        <div class="px-1 bg-white mb-sm-0">
          <div class="row divider-y" wphFocusTrap>
            <div class="col-md-4">
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Distributeur <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale" wphAutoFocus class="form-control pl-4 "
                      placeholder="Entrez Le Distribiteur" formControlName="fournisseur" />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-truck"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12  my-1 p-0">
                <div class="form-group mb-0">
                  <label for="raisonSociale" class="form-label p-0 col-12">
                    Commande <span class="text-danger">*</span>
                  </label>
                  <div class="input-group picker-input">
                    <input type="text" id="raisonSociale"
                        class="form-control pl-4"
                      formControlName="enteteCommandeAchatGroupe" placeholder="veuillez donner le code commande" #commandeInput />
                    <div class="picker-icons picker-icons-alt">
                      <i class="bi bi-receipt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="row">
                <div class="col-12  my-1">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="number" id="raisonSociale"
                            formControlName="montantSaisi" class="form-control pl-4"
                            placeholder="Entrez Montant BL" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-cash"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Remise <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="tauxRf" placeholder="Entrez Remise"
                            />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-gift"></i>
                          </div>
                          <div class="picker-icons picker-icons-end" style="right: 2px;">
                            <i class="bi bi-percent" style="font-size: 15px;opacity: 0.85;color: black;"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-0">
                        <label for="raisonSociale" class="form-label p-0 col-12">
                          Montant BL Calculé <span class="text-danger">*</span>
                        </label>
                        <div class="input-group picker-input">
                          <input type="text" id="raisonSociale"
                            class="form-control pl-4" formControlName="cumulBl" placeholder="Entrez Cumul BL" [readOnly]="true" />
                          <div class="picker-icons picker-icons-alt">
                            <i class="bi bi-search text-dark"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="transporteur" class="form-label p-0 col-12">Transporteur</label>
                    <div class="input-group picker-input">
                      <input type="text" id="transporteur" class="form-control pl-4"
                        formControlName="transporteur" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">N° BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group picker-input">
                      <input type="text" id="raisonSociale" class="form-control pl-4" formControlName="numeroBl"
                        placeholder="Entrez N° BL" />
                      <div class="picker-icons picker-icons-alt">
                        <i class="bi bi-hash text-dark"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group mb-0">
                    <label for="raisonSociale" class="form-label p-0 col-12">
                      Date De BL<span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                      <input type="text" [readOnly]="true" class="form-control form-control-md"
                        formControlName="dateReceptionBl" id="dateDebut" [disabled]="true">
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="card-footer p-0">
    <div class="card bg-transparent my-1 w-100 text-dark rounded">
      <div class="card-body bg-white px-1">
        <div *ngIf="notDispatched &&  !isLoading">
          <div class="card">
            <div class="card-body text-center">
              <h2>Choisissez la méthode de répartition des produits</h2>
              <p class="font-18">Sélectionnez comment vous souhaitez répartir les produits entre les membres du groupe :</p>
              <div class="d-flex align-items-center justify-content-center k-gap-2">
                <button class="btn btn-sm btn-success m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('P')">
                  <span>Donner le reste à ce qui est le plus commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus grande quantité commandée."></i>
                </button>
                <button class="btn btn-sm btn-outline-dark m-1 btn-fs-size d-flex k-gap-2 font-18" (click)="dispatchMode('S')">
                  <span>Donner le reste à ce qui est le moins commandé</span>
                  <i class="bi bi-info-circle" ngbTooltip="Attribuer les surplus aux commandes avec la plus petite quantité commandée."></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        <kendo-grid [data]="gridData" *ngIf="!notDispatched" wphScrollCheck   wphFocusTrapPrefixed prefix="qlivree-"  [rowClass]="rowClass"   class="fs-grid fs-grid-white bl-grid dispatch-grid" style="min-height: 400px;max-height: calc(100vh - 400px)">
          <kendo-grid-column-group title="Liste des produits" headerClass="end-phase">
            <kendo-grid-column field="designation" title="Designation Produit" [width]="300"  class="text-wrap">
              <ng-template kendoGridCellTemplate let-dataItem>
               <div class="d-flex align-items-center k-gap-1" >
                {{dataItem.designation}}  <span *ngIf="dataItem.isCadeau" class="text-success font-weight-bold">(Offert)</span>
                <span *ngIf="dataItem?.isvalidated === undefined; else inValidTemplate" class="text-success font-weight-bold ml-auto">
                  <i class="bi bi-check-circle-fill" style="color: #007514; font-size: 20px; line-height: 1;"></i>
                </span>
                <ng-template #inValidTemplate>
                  <span class="text-danger font-weight-bold ml-auto" ngbTooltip="{{dataItem.raison}}" [placement]="'bottom'">
                    <i class="bi bi-question-circle-fill" style="color: #D92D20; font-size: 20px; line-height: 1;"

                    ></i>
                  </span>
                </ng-template>
               </div>
              </ng-template>
              <ng-template kendoGridFooterTemplate let-column>
                <span class="text-success font-weight-bold">Total:</span>
               </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteCommandee" title="Qté Cmd" [width]="80" class="text-wrap">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteCommandee']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column [hidden]="!isCoffretEnabled" field="qteFixePrdInCoffret" class="text-center" title="Qté Fixe" [width]="100">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{ dataItem?.qteFixePrdInCoffret | number: '1.0-0' }}
              </ng-template>

              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['qteFixePrdInCoffret']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="quantiteLivree" title="Qté Livré" [width]="100">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteLivree']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="quantiteUg" title="Ug Livré" [width]="100" class="end-phase" footerClass="end-phase" headerClass="end-phase">
              <ng-template kendoGridFooterTemplate let-dataItem let-rowIndex="rowIndex">
                <span class="d-block text-right font-weight-bold">{{generateTotals()['quantiteUg']?.sum ?? 0}}</span>
              </ng-template>
            </kendo-grid-column>
           </kendo-grid-column-group>

            <kendo-grid-column-group *ngFor="let member of listMembers;let index = index" [title]="isCoffretEnabled ? member.nom + ' (' + memeberSummary.get(member.id)?.coffretCommandee + ' Coffrets)': member.nom" class="member-block" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase-member  font-weight-bold':true}">

              <kendo-grid-column field="drioich_cmd" title="Cmd" [width]="100" class="text-center" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem >
                    {{getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id)?.quantiteCommandee}}
                  </ng-template>
                  <ng-template kendoGridFooterTemplate let-column>
                    <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteCommandee}}</span>
                   </ng-template>
               </kendo-grid-column>
              <kendo-grid-column field="drioich_livre" title="Q Livré" [width]="100" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0}">
                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                    <input type="text" class="form-control text-center font-weight-bold text-dark"
                    [(ngModel)]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteLivree"
                    [readOnly]="isAllValidated"  [attr.data-prefix]="'qliv'+member.id+'ree-'" id="qliv{{member.id}}ree-{{rowIndex}}"
                    (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteLivree',$event)"
                    wphAllowOnlyNumbers [maxValue]="99999999"
                    [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteLivree') ? '#ccc' : 'inherit'}"
                    >
                  </div>
                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteLivree}}</span>
                 </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="drioich_cmd" title="UG Livré" [width]="100" class="text-center end-phase-member" footerClass="end-phase" headerClass="end-phase-member" [class]="index % 2 === 0 ? 'even-row' : 'odd-row'" [headerClass]="{'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0,'end-phase':index +1 !== listMembers.size}">

                <ng-template  kendoGridCellTemplate let-dataItem  let-rowIndex="rowIndex" >
                  <div class="input-group picker-input">

                  <input type="text" class="form-control text-center font-weight-bold text-dark"
                  [value]="getBlUnitaireLigneByBlocId(dataItem.blocOffreId,member.id).quantiteUg"
                   [readOnly]="isAllValidated"
                   [attr.data-prefix]="'qug'+member.id" id="qu{{member.id}}g-{{rowIndex}}"
                  (input)="updateFieldInDetail(dataItem.blocOffreId,member.id,'quantiteUg',$event)"
                  wphAllowOnlyNumbers [maxValue]="99999999"
                  [ngStyle]="{'background-color': fieldHasChanged(dataItem.blocOffreId,member.id,'quantiteUg') ? '#ccc' : 'inherit'}"
                  >
                </div>

                </ng-template>
                <ng-template kendoGridFooterTemplate let-column>
                  <span class="font-weight-bold d-block text-right">{{memeberSummary.get(member.id)?.quantiteUg}}</span>
                 </ng-template>
              </kendo-grid-column>
            </kendo-grid-column-group>
        </kendo-grid>
      </div>
    </div>
  </div>
</div>
</ng-template>




<ng-template #ChoseBLUnitaire let-modal>
  <div class="modal-header">
      <h4 class="modal-title text-dark" id="modal-basic-title">{{ 'Consulter les bons de sortie' | uppercase }}</h4>
      <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
          <i class="mdi mdi-close"></i>
      </button>
  </div>

  <div class="modal-body">

    <div class="d-flex  py-1 justify-content-end align-items-center">
      <button type="button" class="btn btn-sm btn-success m-1 font-16" (click)="sendBLSUnitaire()"  style="padding-block: 6px; border-radius: 8px;" >
        <i class="bi bi-send"></i>
        Envoyes Les Bons de sortie
      </button>
    </div>
    <kendo-grid [data]="enteteBlUnitaire"   [selectable]="{mode: 'multiple'}"
    [(selectedKeys)]="selectedKeys"
    kendoGridSelectBy="id"
    (cellClick)="OnSelectionChange($event)"
    class="fs-grid fs-grid-white"
    style="min-height: 300px;"

    >
      <kendo-grid-column field="numeroBl" title="N° BL" [width]="80">
      </kendo-grid-column>

      <kendo-grid-column field="codeCommande" title="N° Cmd" [width]="100">
      </kendo-grid-column>

      <kendo-grid-column field="dateCommande" title="Date Cmd" [width]="150">

        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCommande | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Raison Sociale" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
         PH. {{dataItem.enteteCommandeAchatGroupe.client.raisonSociale}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column title="Nom Pharmacien" [width]="150" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
        Dr. {{dataItem.enteteCommandeAchatGroupe.client.nomResponsable}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateReceptionBl" title="Date BL" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateReceptionBl | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="dateCreation" title="Crée le" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          {{dataItem.dateCreation | date: 'dd/MM/yyyy'}}
        </ng-template>
      </kendo-grid-column>

      <kendo-grid-column field="fournisseur.raisonSociale" title="Distributeur" [width]="120"></kendo-grid-column>

      <kendo-grid-column field="montantSaisi" title="Montant BL" [width]="120" class="text-right">
      </kendo-grid-column>


      <kendo-grid-column field="etatBl" title="Statut" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <app-element-status
            [state]="(dataItem?.etat === 'cloturee') ? 'C' : dataItem.etatBl === 'ANNULE' ? 'BANNULE' : dataItem.etatBl === 'VALIDE' ? 'BVALIDE' : dataItem.etatBl"></app-element-status>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>

  </div>
</ng-template>






<ng-template #ModalShowMoreMembers let-modal>
  <div class="modal-header">
    <h4 class="modal-title text-dark" id="modal-basic-title">Liste des membres</h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">
      <i class="mdi mdi-close"></i>
    </button>
  </div>
  <div class="modal-body">
    <kendo-grid [data]="membersHasNoEmail" wphScrollCheck
       class="fs-grid fs-grid-white"
    style="max-height: 300px; min-height: 200px;"
    >
      <kendo-grid-column field="nom" title="Nom" [width]="100" class="text-wrap">
        <ng-template kendoGridCellTemplate let-dataItem>
           DR. {{dataItem.nom}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="raisonSociale" title="Raison Sociale" class="text-wrap" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          PH. {{dataItem.raisonSociale}}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column field="email" title="Email" [width]="100">
        <ng-template kendoGridCellTemplate let-dataItem>
          <span *ngIf="dataItem.email; else: emptyEmail">
            <span *ngIf="dataItem.email.length > 0; else: emptyEmail">{{dataItem.email}}</span>
          </span>
          <ng-template #emptyEmail>
            <span class="text-warning d-flex align-items-center k-gap-1">
              <i class="bi bi-exclamation-triangle" style="line-height: 1;"></i>
              indisponible
            </span>
          </ng-template>
        </ng-template>
      </kendo-grid-column>
    </kendo-grid>
  </div>
</ng-template>
