<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/commandes/1"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Synthése Commande'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row class="ion-no-padding">
      <ion-col>
        <ion-label class="ion-text-nowrap label-sm">N°:</ion-label>
        <ion-item class="ion-no-padding" lines="none" input>
          <ion-input type="number"  value="123456" class="cmd-inp-lg"></ion-input>
        </ion-item>
      </ion-col>
      <ion-col>
        <ion-label class="ion-text-nowrap label-sm">saisie le :</ion-label>
        <ion-item class="ion-no-padding" lines="none" input>
        <ion-input type="text"  value="22/02/2022" class="cmd-inp-lg"></ion-input>
      </ion-item>
      </ion-col>
      <ion-col>
        <ion-label class="ion-text-nowrap label-sm">traitée le :</ion-label>
        <ion-item class="ion-no-padding" lines="none" input>
        <ion-input type="text"  value="22/02/2022" class="cmd-inp-lg"></ion-input>
      </ion-item>
      </ion-col>
    </ion-row>
  </ion-grid>
  <ion-list lines="none" class="w-100">
    <ion-item class="ion-list-item w-100">
      <div class="title-wrapper">
        <div class="left-part">
          <ion-label class="ion-text-wrap"><h2 class="font-bold">GSK</h2></ion-label>
          <div class="inner-part">
            <ion-badge [ngClass]="{'via-direct': true,'via-special':true }" class="ion-float-start badge-option-lg ">Via Grossiste</ion-badge>
            <span>Sophaca</span>
          </div>
        </div>
        <ion-badge [ngClass]="{'type-gold': true}"  class="ion-float-end badge-option-xsm">RF</ion-badge>
      </div>
    </ion-item>
  </ion-list>
  <ion-grid>
    <ion-row class="ion-no-padding">
      <ion-col size="3"></ion-col>
      <ion-col size="3" class="font-bold ion-text-center">Brut</ion-col>
      <ion-col size="3" class="font-bold ion-text-center">Net</ion-col>
      <ion-col size="3" class="font-bold ion-text-center">Remise</ion-col>

    </ion-row>
    <ion-row class="py-confirm">
      <ion-col size="3" >Pack N°: 1</ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">120.30 DH</ion-label></ion-col>
      <ion-col size="3">
        <ion-label class="item-column">100.00 DH</ion-label></ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">20.30 DH</ion-label></ion-col>
    </ion-row>
    <ion-row class="py-confirm">
      <ion-col size="3" >Pack N°: 2</ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">120.30 DH</ion-label></ion-col>
      <ion-col size="3">
        <ion-label class="item-column">100.00 DH</ion-label></ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">20.30 DH</ion-label></ion-col>
    </ion-row>
    <ion-row class="py-confirm">
      <ion-col size="3" >Pack N°: 3</ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">120.30 DH</ion-label></ion-col>
      <ion-col size="3">
        <ion-label class="item-column">100.00 DH</ion-label></ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">20.30 DH</ion-label></ion-col>
    </ion-row>
    <ion-row class="py-confirm">
      <ion-col size="3" >Pack N°: 4</ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">120.30 DH</ion-label></ion-col>
      <ion-col size="3">
        <ion-label class="item-column">100.00 DH</ion-label></ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">20.30 DH</ion-label></ion-col>
    </ion-row>
  </ion-grid>


  <div class="cmd-cmd-footer-conf ion-no-padding">
    <ion-grid class="ion-no-padding">
      <ion-row class="footer-wrapper ion-padding-horizontal">
        <ion-col size="7" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-lg">Montant Total Net</ion-label>
           <ion-input type="number"  value="178600.00" class="footer-inp-lg"></ion-input>
        </ion-col>
        <ion-col size="5" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-sm">Soit une remise</ion-label>
           <ion-input type="number"  value="100.00" class="footer-inp-sm"></ion-input>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

</ion-content>
