import { HttpClient, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, firstValueFrom, map } from 'rxjs';
import { ContactFournisseurGroupe, Pagination, SearchContactFournCriteria, SearchContactFournisseurGroupe } from '@wph/data-access';
import { Pharmacie } from '../models/pharmacie.model';
import {
  GroupeEntreprise,
  SearchGroupeEntreprise,
} from '../models/groupe-entreprise.model';
import {
  PharmacieEntreprise,
  SearchPharmacieEntreprise,
} from '@wph/federation-syndicats';
import { GroupeEntrepriseCriteria } from '../models/groupe-entreprise-criteria.model';
import { PharmacieEntrepriseCriteria } from '../models/pharmacie-entreprise-criteria.model';
import {
  SearchSuggestionFicheClient,
  SuggestionFicheClient,
} from '../models/suggestion-fiche-client.model';
import { SuggestionFicheClientCriteria } from '../models/suggestion-fiche-client-criteria.model';
import { AuthService } from '@wph/core/auth';
import { DomainEnumerationDto } from '../models/DomainEnumerationDto';
import { DashboardData, LaborationDashboardData } from '../models/dashboard-data';

@Injectable({
  providedIn: 'root',
})
export class FederationSyndicatService {
  baseUrl: string;

  inactiveAccount$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(null);

  constructor(
    @Inject('ENVIROMENT') private env: any,
    private http: HttpClient,
    private authService: AuthService
  ) {
    this.baseUrl = this.env?.base_url;
  }

  editPharmacie(pharmacie: Pharmacie) {
    return this.http.post<any>(
      this.baseUrl + '/api/officielpharma/pharmacie/',
      pharmacie,
      { observe: 'body' }
    );
  }

  // ? SKIP
  desassocierPharmacie(id: number) {
    return this.http.delete<any>(
      this.baseUrl + '/api/officielpharma/pharmacie/' + id + '/desassocier',
      { observe: 'body' }
    );
  }

  getPageNumber(skip: number, pageSize: number) {
    return skip && pageSize ? Math.floor(skip / pageSize) : 0;
  }

  // ? GESTION GROUPE ENDPOINTS

  saveGroupeEntreprise(
    payload: GroupeEntreprise
  ): Observable<GroupeEntreprise> {
    return this.http.post<GroupeEntreprise>(
      this.baseUrl + '/api/v1/groupe-entreprise',
      payload,
      { observe: 'body' }
    );
  }

  getGroupeEntrepriseById(id_groupe: number): Observable<GroupeEntreprise> {
    return this.http.get<GroupeEntreprise>(
      this.baseUrl + '/api/v1/groupe-entreprise/' + id_groupe,
      { observe: 'body' }
    );
  }

  activerGroupeEntreprise(id_groupe: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_groupe}/activer`,
      { observe: 'body' }
    );
  }

  desactiverGroupeEntreprise(id_groupe: number): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_groupe}/desactiver`,
      { observe: 'body' }
    );
  }

  supprimerGroupeEntreprise(id_groupe: number): Observable<any> {
    return this.http.delete<any>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_groupe}/supprimer`,
      { observe: 'body' }
    );
  }

  searchGroupeEntreprise(
    pagination: Pagination,
    criteria: GroupeEntrepriseCriteria
  ): Observable<SearchGroupeEntreprise> {
    let params = {
      page: String(
        this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0
      ),
      size: String(pagination.pageSize) ?? 20,
    };

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchGroupeEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/search`,
      criteria,
      { params, observe: 'body' }
    );
  }

  getGroupeOfCurrentUser(): Observable<GroupeEntreprise> {
    return this.http.get<GroupeEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/mygroupe`,
      { observe: 'body' }
    );
  }

  async getMyGroupe(): Promise<GroupeEntreprise> {
    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      let principal = this.authService.getPrincipal();

      if (principal['groupe']) {
        return principal['groupe'];
      } else {
        const myGroupe = await firstValueFrom(
          this.getGroupeOfCurrentUser().pipe(map((res) => res))
        );

        principal = this.authService.getPrincipal();

        principal['groupe'] = myGroupe;
        this.authService.updatePrincipal(principal);

        return myGroupe;
      }
    }
    return null
  }

  getGroupesByOffreId(offreId: number): Observable<GroupeEntreprise[]> {
    return this.http.get<GroupeEntreprise[]>(
      `${this.baseUrl}/api/v1/groupe-entreprise/groupe-by-offre/${offreId}`,
      { observe: 'body' }
    );
  }

  // ? GESTION DES MEMBRES DU GROUPE ENDPOINTS

  ajouterMembreAuGroupeEntreprise(
    id_group: number,
    id_membre: any
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_group}/ajouter-membre`,
      {
        params: { membreId: id_membre },
        observe: 'body',
      }
    );
  }

  retirerMembreDuGroupeEntreprise(
    id_membre: number, groupeId?: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${groupeId}/retirer-membre`, { observe: 'body', params: { membreId: id_membre } }
    );
  }

  activerMembreDuGroupeEntreprise(
    id_membre: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_membre}/activer-membre`,
      { observe: 'body' }
    );
  }

  desactiverMembreDuGroupeEntreprise(
    id_membre: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_membre}/desactiver-membre`,
      { observe: 'body' }
    );
  }

  promoteMembreDuGroupeEntreprise(
    id_groupe: number,
    id_membre: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_groupe}/promote-membre`,
      {
        params: { membreId: id_membre },
        observe: 'body',
      }
    );
  }

  getMembresDuGroupeEntrepriseById(
    id_groupe: number
  ): Observable<PharmacieEntreprise[]> {
    return this.http.get<PharmacieEntreprise[]>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${id_groupe}/membres`
    );
  }

  getMembreDuGroupeById(id_membre: number): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/membre/${id_membre}`,
      { observe: 'body' }
    );
  }

  // ? GESTION DE PHARMACIES ENDPOINTS

  activerPharmacieEntreprise(
    id_pharmacie: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/activer/${id_pharmacie}`,
      { observe: 'body' }
    );
  }

  desactiverPharmacieEntreprise(
    id_pharmacie: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/desactiver/${id_pharmacie}`,
      { observe: 'body' }
    );
  }

  searchPharmacieEntreprise(
    pagination: Pagination,
    criteria: PharmacieEntrepriseCriteria
  ): Observable<SearchPharmacieEntreprise> {
    let params = {
      page: String(
        this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0
      ),
      size: String(pagination.pageSize) ?? 20,
    };

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchPharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/search`,
      criteria,
      { params, observe: 'body' }
    );
  }

  activerMembreGroupeEntreprise(
    groupeId: number,
    membreId: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${groupeId}/activer-membre`,
      { observe: 'body', params: { membreId } }
    );
  }

  desactiverMembreGroupeEntreprise(
    groupeId: number,
    membreId: number
  ): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/groupe-entreprise/${groupeId}/desactiver-membre`,
      { observe: 'body', params: { membreId } }
    );
  }

  getPharmacieById(id_pharmacie): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/getbyid/${id_pharmacie}`,
      { observe: 'body' }
    );
  }

  envoyerIdentifiantsParMail(ids: number[]): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/api/v1/users/envoyer-credentials`,
      {
        params: { recipientEntrepriseId: ids },
        observe: 'body',
      }
    );
  }

  envoyerIdentifiantsParWhatsapp(ids: number[]): Observable<any> {
    return this.http.get<any>(
      `${this.baseUrl}/api/v1/users/envoyer-whatsapp`,
      {
        params: { recipientEntrepriseId: ids },
        observe: 'body',
      }
    );
  }



  // ? GESTION DES SUGGESTIONS DES FICHES PHARMACIES ENDPOINTS

  getPharmacieEntrepriseById(id: number): Observable<PharmacieEntreprise> {
    return this.http.get<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/get-suggestion-byid/${id}`,
      { observe: 'body' }
    );
  }

  saveFichePharmacieEntreprise(
    payload: SuggestionFicheClient
  ): Observable<SuggestionFicheClient> {
    return this.http.post<SuggestionFicheClient>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/save-suggestion`,
      payload,
      { observe: 'body' }
    );
  }

  savePharmacieMaroc(
    payload: SuggestionFicheClient
  ): Observable<PharmacieEntreprise> {
    return this.http.post<PharmacieEntreprise>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/save-pharmacie`,
      payload,
      { observe: 'body' }
    );
  }

  searchSuggestionsFichePharmacie(
    pagination: Pagination,
    criteria: SuggestionFicheClientCriteria
  ): Observable<SearchSuggestionFicheClient> {
    let params = {
      page: String(
        this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0
      ),
      size: String(pagination.pageSize) ?? 20,
    };

    if (pagination.sortField) {
      params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return this.http.post<SearchSuggestionFicheClient>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/search-suggestion`,
      criteria,
      { params, observe: 'body' }
    );
  }

  togglePharmacieDeletionStatus(pharmacieId: number): Observable<void> {
    return this.http.get<void>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/toggle-deletion/${pharmacieId}`,
      { observe: 'body' }
    );
  }

  /**
   * Search Pharmacie based on criteria.
   *
   * @param criteria - The search criteria.
   * @param pagination - Pagination and sorting options.
   * @returns Observable containing the search results.
   */
  searchPharmacie(
    criteria: PharmacieEntrepriseCriteria,
    pagination: { page: number; size: number; sort: string }

  ): Observable<SearchPharmacieEntreprise> {
    const params = {
      page: pagination.page.toString(),
      size: pagination.size.toString(),
      sort: pagination.sort,
    };

    return this.http.post<SearchPharmacieEntreprise>(`${this.baseUrl}/api/v1/pharmacie-entreprise/search-pharmacie`, criteria, { params });
  }

  getSuggestionById(suggestionId: number): Observable<SuggestionFicheClient> {
    return this.http.get<SuggestionFicheClient>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/get-suggestion-byid/${suggestionId}`
    );
  }

  annulerSuggestion(suggestionId: string): Observable<any> {
    return this.http.get(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/annuler-suggestion/${suggestionId}`,
      { observe: 'body' }
    );
  }

  validerSuggestion(suggestionId: number): Observable<SuggestionFicheClient> {
    return this.http.get<SuggestionFicheClient>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/valider-suggestion/${suggestionId}`
    );
  }

  rejeterSuggestion(suggestionId: number): Observable<SuggestionFicheClient> {
    return this.http.get<SuggestionFicheClient>(
      `${this.baseUrl}/api/v1/pharmacie-entreprise/rejeter-suggestion/${suggestionId}`
    );
  }

  listByDomaine(domaine: string): Observable<DomainEnumerationDto[]> {
    const params = new HttpParams().set('domaine', domaine);
    return this.http.get<DomainEnumerationDto[]>(
      `${this.baseUrl}/api/domainenumeration/list`,
      { params, observe: 'body' }
    );
  }

  listAllByDomaine(domaine: string): Observable<DomainEnumerationDto[]> {
    const params = new HttpParams().set('domaine', domaine);
    return this.http.get<DomainEnumerationDto[]>(
      `${this.baseUrl}/api/domainenumeration/list-all`,
      { params, observe: 'body' }
    );
  }

  saveDomainEnumeration(item: DomainEnumerationDto): Observable<any> {
    return this.http.post(`${this.baseUrl}/api/domainenumeration/edit`, item);
  }

  deleteDomaineById(domaineId: number): Observable<any> {
    return this.http.get(`${this.baseUrl}/api/domainenumeration/delete`, { params: { domaineId: domaineId.toString() } });
  }

  restoreDomaineById(domaineId: number): Observable<any> {
    return this.http.get(`${this.baseUrl}/api/domainenumeration/restore`, { params: { domaineId: domaineId.toString() } });
  }

  obtenirTableauDeBord(groupeId: number, membreId: number): Observable<DashboardData> {
    const params = new HttpParams()
      .set('groupeId', groupeId.toString())
      .set('membreId', membreId.toString());

    return this.http.get<DashboardData>(`${this.baseUrl}/api/v1/tableauDeBord`, { params });
  }

  getTableauDeBordLaboratoire(): Observable<LaborationDashboardData> {
    return this.http.get<LaborationDashboardData>(`${this.baseUrl}/api/v1/tableauDeBord-laboratoire`);
  }

  getTotalActiveGroups(): Observable<number> {
    return this.http.get<number>(`${this.baseUrl}/api/v1/groupe-entreprise/totals/active-groups`);
  }

  getTotalActiveMembers(): Observable<number> {
    return this.http.get<number>(`${this.baseUrl}/api/v1/groupe-entreprise/totals/active-members`);
  }
  getTotalActiveMembersByGroupe(groupeId: number): Observable<number> {
    return this.http.get<number>(`${this.baseUrl}/api/v1/groupe-entreprise/${groupeId}/totals/active-members`);
  }

  synchroniserBasePharmacie(): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/api/conso_ext/batch/phmaroc`, { observe: 'body' });
  }

  // ? GESTION CONTACT FOURNISSEUR START
  searchContactsFournisseurGroupe(pagination: Pagination, criteria: SearchContactFournCriteria): Observable<SearchContactFournisseurGroupe> {
    let params = {
      pageSize: String(pagination.pageSize) ?? 20,
      pageNumber: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
    };

    pagination.sortField && (params['sort'] = pagination.sortField + ',' + pagination.sortMethod);

    return this.http.post<SearchContactFournisseurGroupe>(`${this.baseUrl}/api/v1/contact-fournisseur-groupe/search`, criteria, { params })
  }

  saveContactFournisseurGroupe(newContact: ContactFournisseurGroupe): Observable<ContactFournisseurGroupe> {
    return this.http.post<ContactFournisseurGroupe>(`${this.baseUrl}/api/v1/contact-fournisseur-groupe/save`, newContact);
  }

  getContactFournisseurGroupeById(id: number): Observable<ContactFournisseurGroupe> {
    return this.http.get<ContactFournisseurGroupe>(`${this.baseUrl}/api/v1/contact-fournisseur-groupe/get-by-id/${id}`);
  }

  deleteContactFournisseurGroupeById(id: number): Observable<ContactFournisseurGroupe> {
    return this.http.delete<ContactFournisseurGroupe>(`${this.baseUrl}/api/v1/contact-fournisseur-groupe/delete/${id}`);
  }

  // ? GESTION CONTACT FOURNISSEUR END

}
