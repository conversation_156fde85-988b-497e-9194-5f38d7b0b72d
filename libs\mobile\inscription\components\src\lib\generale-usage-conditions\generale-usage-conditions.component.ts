import {Component, ElementRef, OnInit, ViewChild, ViewEncapsulation} from '@angular/core';
import {ModalController} from "@ionic/angular";

@Component({
  selector: 'wph-generale-usage-conditions',
  templateUrl: './generale-usage-conditions.component.html',
  styleUrls: ['./generale-usage-conditions.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class GeneraleUsageConditionsComponent implements OnInit {
  @ViewChild('terms') private myScrollContainer: ElementRef;

  isScrolled = false;
  constructor(private modalController: ModalController) {}

  ngOnInit(): void {}

  handleScrollClick() {
    try {
      this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;
    } catch(err) { }
  }

  onScroll(event: any) {
    if (event.target.offsetHeight + event.target.scrollTop + 10 >= event.target.scrollHeight) {
      this.isScrolled = true;
      console.log("End");
    }
  }
  accept() {
    this.modalController.dismiss(true);
  }
}
