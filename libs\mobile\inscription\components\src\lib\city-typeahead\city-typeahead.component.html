<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="cancelChanges(); (search.value = null);">Annuler</ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="confirmChanges(); (search.value = null);">Confirmer</ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar>
    <ion-searchbar id="search-bar" [debounce]="300" placeholder="Rechercher une ville" (ionChange)="searchbarInput($event)"
      #search></ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding-top">
  <ion-list lines="full" id="modal-list" [inset]="true">
    <ion-radio-group [value]="selectedCity" (ionChange)="radioChange($event)">
      <ion-item lines="full" [id]="city?.id" *ngFor="let city of filteredCities; trackBy: trackItems">
        <ion-label>{{ city?.labelFr }}</ion-label>
        <ion-radio slot="start" [value]="city"></ion-radio>
      </ion-item>
    </ion-radio-group>

    <div class="first-load-container" *ngIf="isLoading">
      <ion-spinner></ion-spinner>
    </div>
  </ion-list>

  <wph-empty-list *ngIf="!isLoading && !filteredCities.length" message="Aucun résultat trouvé"></wph-empty-list>
</ion-content>