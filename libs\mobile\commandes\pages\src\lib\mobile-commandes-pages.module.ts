import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListCommandesPage } from './list-commandes/list-commandes.page';
import { CommandePage } from './commande/commande.page';
import { MobileCommandesPagesRoutingModule } from "./mobile-commandes-pages-routing.module";
import { MobileCommandesComponentsModule } from "@wph/mobile/commandes/components";
import { MobileSharedModule } from "@wph/mobile/shared";
import { ConfirmationCommandePage } from './confirmation-commande/confirmation-commande.page';
import { MesCommandesPage } from './mes-commandes/mes-commandes.page';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SyntheseCommandePage } from './synthese-commande/synthese-commande.page';

@NgModule({
  imports: [CommonModule, MobileCommandesComponentsModule, MobileCommandesPagesRoutingModule, MobileSharedModule,FormsModule, ReactiveFormsModule],
  declarations: [
    ListCommandesPage,
    CommandePage,
    ConfirmationCommandePage,
    MesCommandesPage,
    SyntheseCommandePage
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MobileCommandesPagesModule {}
