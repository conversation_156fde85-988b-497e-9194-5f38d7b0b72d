import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { NavController, Platform } from '@ionic/angular';
import { Subscription } from 'rxjs';

@Component({
  selector: 'wph-list-commandes',
  templateUrl: './list-commandes.page.html',
  styleUrls: ['./list-commandes.page.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class ListCommandesPage implements OnInit, OnDestroy {
  backBtnSubscription: Subscription | null = null;

  constructor(
    private platform: Platform,
    private navController: NavController
  ) { }

  ngOnInit(): void {
    this.backBtnSubscription = this.platform.backButton.subscribeWithPriority(10, (processNextHandler) => {
      this.navController.navigateBack('/accueil'), processNextHandler();
    });
  }

  openPage(page:string){
    this.navController.navigateForward([page], {});
  }

  ngOnD<PERSON>roy(): void {
    this.backBtnSubscription.unsubscribe();
  }
}
