import {
  AfterViewInit,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CellClickEvent, GridComponent, GridDataResult, SelectableSettings, SelectionEvent } from '@progress/kendo-angular-grid';
import { Pagination } from '@wph/data-access';
import {
  AlertService,
  ICity,
  PlateformeService,
  Societe,
  SocieteType,
  StaticDataService,
} from '@wph/shared';
import {
  Observable,
  Subject,
  debounceTime,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  iif,
  map,
  of,
  switchMap,
  takeUntil,
} from 'rxjs';
import {
  PharmacieEntreprise,
  SearchPharmacieEntreprise,
} from '../../../models/pharmacie-entreprise.model';
import { PharmacieEntrepriseCriteria } from '../../../models/pharmacie-entreprise-criteria.model';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ScrollService, UserInputService } from '@wph/web/shared';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { Localite } from 'libs/shared/src/lib/models/localite.model';
import { FsStatistiqueService } from '../../../services/fs-statistique.service';
import { StatisticsCriteriaDTO } from '../../../models/statistique.model';
import { AuthService } from '@wph/core/auth';
import { SelectedPlateforme } from '@wph/web/layout';

type PageMode = 'S' | 'P';

@Component({
  selector: 'wph-saisie-groupe',
  templateUrl: './saisie-groupe.component.html',
  styleUrls: ['./saisie-groupe.component.scss'],
})
export class SaisieGroupeComponent implements OnInit, AfterViewInit, OnDestroy {
  mode: PageMode = 'S';
  pageTitle: string = '';
  villes: ICity[];
  navigation: Pagination = { pageSize: 10, skip: 0 };
  searchNavigation: Pagination = { pageSize: 10, skip: 0 };
  gridData: GridDataResult = { total: 0, data: [] };
  filterResult: PharmacieEntreprise[] = [];
  localites: Localite[];
  modeEnvoiIdentifiants: string;
  targetPharmacie: PharmacieEntreprise;
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  gridSelectable: boolean | SelectableSettings = { mode: 'multiple', checkboxOnly: true };

  isMembreModal: boolean;

  groupeId: number;
  readOnly: boolean;
  groupeForm: FormGroup;
  filterForm: FormGroup;

  listeGroupeMode: string;
  membersGridData: GridDataResult = { total: 0, data: [] };

  members: PharmacieEntreprise[] = [];
  fetchGroupeDone: boolean = false;
  fetchMembersDone: boolean = false;

  inputRef: HTMLInputElement | null = null;
  modified: boolean = false;
  hasMore: boolean;

  selectedKeys: number[] = [];
  currentPlateforme: SelectedPlateforme;

  stautsLabelsValues: any[] = [
    { label: 'Tout', value: null },
    { label: 'Déjà Membre', value: true },
    { label: 'Disponible', value: false },
  ];

  @ViewChild('largeInput') largeInput: ElementRef;
  @ViewChild('grid') grid: GridComponent;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private authService: AuthService,
    private alertService: AlertService,
    private scrollService: ScrollService,
    private userInputService: UserInputService,
    private plateformeService: PlateformeService,
    private staticDataService: StaticDataService,
    private fsStatistiqueService: FsStatistiqueService,
    private federationSyndicatService: FederationSyndicatService
  ) {
    this.initForm();
    this.initFilterForm();
    this.currentPlateforme = this.plateformeService.getCurrentPlateforme();
  }

  get f() {
    return this.groupeForm?.controls;
  }

  ngAfterViewInit(): void {
    this.inputRef = this.largeInput?.nativeElement as HTMLInputElement;
  }

  ngOnInit(): void {
    const qParams = this.route.snapshot.queryParams;
    this.listeGroupeMode = qParams['mode'];
    this.filterResult = [];

    this.getListeVilles();
    this.getListeLocalites();

    this.route.params.subscribe((params) => {
      const id = params['id'];

      if (qParams['readOnly']) {
        this.readOnly = JSON.parse(qParams['readOnly']);
      }

      if (id) {
        this.groupeId = +id;
        this.readOnly && (this.mode = 'P');

        this.pageTitle =
          this.mode === 'S' ? 'Modifier Groupe' : 'Consulter Groupe';

        this.fetchGroupeById(id);
        this.fetchMembersByGroupeId(id);
      } else {
        this.pageTitle = 'Ajouter Groupe';
      }
    });

    this.listenToScrollPosition();
  }

  initForm(): void {
    this.groupeForm = this.fb.group({
      id: [null],
      ville: [null],
      localite: [null],
      pharmacien: [null],
      raisonSociale: [null, [Validators.required]],
      responsablesGroupe: [null],
      statutEntreprise: [true, [Validators.required]],
      typeEntreprise: ['GROUPE_CLIENT'],
    });
  }

  initFilterForm(): void {
    this.filterForm = this.fb.group({
      raisonSociale: [null],
      nomResponsable: [null],
      ville: [null],
      localite: [null],
      statutMembreEntreprise: [null],
    });
  }

  searchTerm: string = '';

  appliquerFiltre(loadMore = false): void {
    const { ville, raisonSociale, localite, ...payload } = this.filterForm.getRawValue();
    this.searchTerm = raisonSociale || '';

    const criteria = new PharmacieEntrepriseCriteria({
      ...payload,
      raisonSociale: raisonSociale,
      ville: ville?.labelFr || ville,
      localite: localite || '',
      typeEntreprises: [SocieteType.CLIENT],
    });

    this.federationSyndicatService
      .searchPharmacieEntreprise(this.searchNavigation, criteria)
      .subscribe({
        next: (res) => {
          this.hasMore = !res?.last;

          if (loadMore) {
            this.filterResult.push(...res?.content);
          } else {
            this.filterResult = res?.content || [];
          }
        },
        error: (err) => {
          console.error('Error fetching results:', err);
        }
      });
  }

  listenToScrollPosition(): void {
    this.scrollService.reachedBottom$
      .pipe(
        takeUntil(this.unsubscribe$),
        filter((state) => !!state)
      )
      .subscribe((_state) => {
        if (this.hasMore) {
          if (
            this.searchNavigation.skip !==
            (this.searchNavigation.skip + this.searchNavigation.pageSize)
          ) {
            this.searchNavigation.skip += this.searchNavigation.pageSize;

            this.appliquerFiltre(true);
          }
        }
      });
  }

  viderFiltre(): void {
    this.filterForm.reset();
    this.filterResult = [];
    this.searchTerm = '';

    this.hasMore = false;
    this.searchNavigation.skip = 0;

  }

  selectAll(): void {
    this.inputRef.select();
  }

  getListeVilles(): void {
    this.staticDataService.getListCitiesByCountryId(1).subscribe((res) => {
      this.villes = res;
    });
  }

  filterListePharmacien(term: string): Observable<SearchPharmacieEntreprise> {
    const payload = new PharmacieEntrepriseCriteria({
      nomResponsable: term?.toLowerCase(),
      typeEntreprises: [SocieteType?.CLIENT],
    });

    return this.federationSyndicatService.searchPharmacieEntreprise(
      { pageSize: 10, skip: 0 },
      payload
    );
  }

  responsablesGroupe: any[] = [];

  fetchGroupeById(id: number): void {
    this.federationSyndicatService
      .getGroupeEntrepriseById(id)
      .subscribe((res) => {
        this.groupeForm.patchValue(
          {
            id: res?.id,
            localite: res?.localite,
            ville: this.findVilleObjByName(res?.ville),
            raisonSociale: res?.raisonSociale,
            statutEntreprise: res?.statutEntreprise,
            pharmacien: res?.responsablesGroupe[0],
            responsablesGroupe: this.fb.array(res?.responsablesGroupe),
          },
          { emitEvent: true }
        );
        this.responsablesGroupe = res?.responsablesGroupe || [];
        this.fetchGroupeDone = true;
        this.combineGridData();
      });
  }

  fetchMembersByGroupeId(groupId: number): void {
    this.federationSyndicatService
      .getMembresDuGroupeEntrepriseById(groupId)
      .subscribe((members: PharmacieEntreprise[]) => {
        this.members = members.map((member) => {
          member['role'] = 'member';
          return member;
        });
        this.fetchMembersDone = true;
        this.combineGridData();
      });
  }

  combineGridData(): void {
    if (this.fetchGroupeDone && this.fetchMembersDone) {
      // Filter out members who are also chefs
      const filteredMembers = this.members.filter(
        (member) =>
          !this.responsablesGroupe.some(
            (responsable) => responsable.id === member.id
          )
      );
      // Assign role to responsablesGroupe as 'chef'
      const responsablesWithRole = this.responsablesGroupe.map((chef) => {
        chef['role'] = 'chef';
        return chef;
      });
      this.gridData = {
        total: responsablesWithRole.length + filteredMembers.length,
        data: [...responsablesWithRole, ...filteredMembers],
      };
    }
  }

  findVilleObjByName(term: string) {
    return this.villes?.filter(
      (ville) => ville?.labelFr.toLowerCase() === term?.toLowerCase()
    )[0];
  }

  getListeLocalites(): void {
    this.staticDataService.getListLocalitiesByCountryId().subscribe((res) => {
      this.localites = res;
      this.groupeForm.patchValue({
        localite: this.localites.find(
          (v) => v.localite === this.groupeForm?.value?.localite
        ),
      });
    });
  }

  attacherMembre(membre: PharmacieEntreprise): void {
    this.federationSyndicatService.ajouterMembreAuGroupeEntreprise(this.groupeId, membre?.id).subscribe(res => {
      this.alertService.successAlt(`La pharmacie: ${membre?.raisonSociale} a été attachée comme membre du groupe avec succès.`, 'Membre Attaché', 'MODAL');

      this.appliquerFiltre();
      this.fetchMembersByGroupeId(this.groupeId);
    });
  }

  detacherMembre(membre: PharmacieEntreprise): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir détacher la pharmacie <b>${membre?.raisonSociale}</b> du groupe ?`).then(
        () => {
            this.modalService.dismissAll();

            this.federationSyndicatService.retirerMembreDuGroupeEntreprise(membre.id, this.groupeId).subscribe(res => {
              this.fetchMembersByGroupeId(this.groupeId);
                this.alertService.successAlt(`La pharmacie <b>${membre?.raisonSociale}</b> a été détachée du groupe avec succès.`, 'Membre Detaché', 'MODAL');
            });
        },
        () => null
    );
}

  selectMembre(item: PharmacieEntreprise): void {
    const selected: PharmacieEntreprise = item;

    if (!selected?.statutMembreGroupe) {
        this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir attacher la pharmacie: <b>${selected?.raisonSociale}</b> comme membre au groupe ?`).then(
            () => {
                // this.modalService.dismissAll();

                this.attacherMembre(selected);
            },
            () => null
        );
    }
}

  searchChefGroupe = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        return term?.length > 1
          ? this.filterListePharmacien(term)
          : of({ content: [] });
      }),
      map((res) => {
        return res?.content?.slice(0, 10);
      })
    );

  selectItem(item: PharmacieEntreprise): void {
    if (!item?.statutMembreGroupe) {
      const chefDuGroupe: PharmacieEntreprise = this.f['pharmacien'].value;
      const selected: PharmacieEntreprise = { ...item, statutEntreprise: true };

      if (this.gridData?.total) {
        this.userInputService
          .confirmAlt(
            'Confirmation',
            `Êtes vous sûr de vouloir modifier le responsable du groupe ?`
          )
          .then(
            () => {
              this.verifierBalanceDeChefDuGroupe().then(hasZeroBalance => {
                if (hasZeroBalance) {
                  this.setSelectedItem(selected);
                  this.modalService.dismissAll();

                  this.viderFiltre();
                  this.alertService.successAlt(`Le responsable du groupe a été modifié avec succès. Veuillez enregistrer les changements pour valider ce choix.`, 'Responsable du groupe modifié', 'MODAL');
                } else {
                  this.alertService.error(`Impossible de modifier le Responsable du groupe <b>Dr. ${chefDuGroupe?.nomResponsable}</b> car sa balance n'est pas soldée.`, 'MODAL');
                }
              });
            },
            () => null
          );
      } else {
        this.setSelectedItem(selected);
        this.modalService.dismissAll();

        this.viderFiltre();
      }
    }
  }

  setSelectedItem(item: PharmacieEntreprise): void {
    this.f['pharmacien'].setValue(item);

    if (this.gridData?.total) {
      const itemAtIndex0 = this.gridData.data[0];

      let gridData = this.gridData?.data;

      (itemAtIndex0?.role !== 'member') && gridData.shift();

      gridData = [item, ...gridData];

      this.gridData = {
        data: gridData,
        total: gridData.length
      };
    } else {
      this.gridData = {
        data: [item],
        total: 1
      };
    }

    this.f['responsablesGroupe'] = this.fb.array([item]);
  }

  chefOuMembreFormatter = (result: Societe) => `${result?.nomResponsable}`;

  searchMembre = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        return term?.length > 1
          ? this.filterListePharmacien(term)
          : of({ content: [] });
      }),
      map((res) => res?.content)
    );

  searchVilleOuLocalite = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        return term?.length < 1
          ? of([])
          : of(
            this.villes?.filter((ville) =>
              ville?.labelFr?.toLowerCase()?.includes(term?.toLowerCase())
            )
          );
      }),
      map((res) => res?.slice(0, 10))
    );

  searchLocalite = (text$: Observable<string>) => {
    return text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      map((term) =>
        term === ''
          ? []
          : this.localites
            .filter(
              (v) => v.localite.toLowerCase().indexOf(term.toLowerCase()) > -1
            )
            .slice(0, 10)
      )
    );
  };

  villeFormatter = (result: ICity) => result?.labelFr;
  localiteFormatter = (result: Localite) => result?.localite;

  back(): void {
    if (this.modified) {
      this.router.navigate(['/achats-groupes/groupes/liste'], {
        state: { modified: true },
        queryParams: { mode: this.listeGroupeMode ? 'cartes' : 'liste' },
      });
    } else {
      this.router.navigate(['/achats-groupes/groupes/liste'], {
        queryParams: { mode: this.listeGroupeMode ? 'cartes' : 'liste' },
      });
    }
  }

  modalRef: NgbModalRef;


  suggererPharmacie(id?: number): void {
    let qParams = { read: this.readOnly, from: 'param-memeber' };

    this.groupeId && (qParams['ref'] = this.groupeId);

    if (id) {
      this.router.navigate([`/achats-groupes/pharmacies/edit/${id}`], { queryParams: { ...qParams, suggerer: true } });
    } else {
      this.router.navigate(['/achats-groupes/pharmacies/suggerer'], { queryParams: qParams });
    }


  }

  suggestPharmacyAndCloseModal() {
    this.suggererPharmacie();
    this.modalService.dismissAll();
  }

  enregistrerGroupe(): void {
    const { pharmacien, ville, localite, responsablesGroupe, ...payload } =
      this.groupeForm?.getRawValue();

    this.federationSyndicatService
      .saveGroupeEntreprise({
        ...payload,
        ville: ville?.labelFr || ville,
        localite: localite?.localite,
        responsablesGroupe:
          (responsablesGroupe as FormArray).value ?? responsablesGroupe,
      })
      .subscribe((res) => {
        this.modified = true;

        if (this.groupeId) {
          this.alertService.successAlt(
            `Le groupe <b>${res?.raisonSociale}</b> a été modifié avec succès.`,
            'Groupe Modifié',
            'MODAL'
          );

          this.back();
        } else {
          this.alertService.successAlt(
            `Le groupe <b>${res?.raisonSociale}<b> a été enregistré avec succès.`,
            'Groupe Enregistré',
            'MODAL'
          );

          setTimeout(() => {
            this.initForm();
            this.initFilterForm();

            this.filterResult = [];
            this.gridData = { total: 0, data: [] };
          }, 500);
        }
      });
  }

  clearGrid(): void {
    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir supprimer le Responsable du groupe ?`
      )
      .then(
        () => {
          this.verifierBalanceDeChefDuGroupe().then(hasZeroBalance => {
            const chefDuGroupe: PharmacieEntreprise = this.f['pharmacien'].value;

            if (hasZeroBalance) {
              this.f['pharmacien'].setValue(null);

              const gridData = this.gridData.data;
              gridData.shift();

              this.gridData = {
                data: gridData,
                total: gridData.length
              };

              this.f['responsablesGroupe'] = this.fb.array([]);

              this.alertService.successAlt(`Le Responsable du groupe, <b>Dr. ${chefDuGroupe?.nomResponsable}</b>, a été supprimé avec succès. Veuillez sélectionner un nouveau responsable du groupe et enregistrer les changements pour valider ce choix.`, 'Responsable du groupe supprimé', 'MODAL');
            } else {
              this.alertService.error(`Impossible de supprimer le responsable du groupe <b>Dr. ${chefDuGroupe?.nomResponsable}</b> car sa balance n'est pas soldée.`, 'MODAL');
            }
          });
        },
        () => null
      );
  }

  async verifierBalanceDeChefDuGroupe(): Promise<boolean> {
    const chefDuGroupe: PharmacieEntreprise = this.f['pharmacien'].value;

    const statsCriteria = new StatisticsCriteriaDTO({ idMembre: chefDuGroupe?.id, idGroupe: this.groupeId });
    const statsMembre = await firstValueFrom(
      this.fsStatistiqueService.getStatictiques(statsCriteria).pipe(map(res => res[0]))
    );

    return statsMembre?.balance === 0;
  }

  sendMailToUser(item: PharmacieEntreprise) {
    if (!item?.gsm1 && !item?.email) {
      this.alertService.identifiantsManquants("Les données de l'utilisateur sélectionné sont incomplètes.", 'Données Indisponible', item?.id);
    } else {
      this.federationSyndicatService.envoyerIdentifiantsParMail([item.id]).subscribe(res => {
        this.alertService.mailSent(`Un message avec login et mot de passe est envoyer à la pharmacie <b>${item?.raisonSociale}</b>`, 'Identifiants Envoyés');
      });
    }
  }

  envoyerIdentifiantsMultiples(toWhatsApp = false): void {
    const selectedPharmacies = this.gridData?.data?.filter((pharmacie: PharmacieEntreprise) => this.selectedKeys?.includes(pharmacie?.id));
    const hasMissingCredentials = selectedPharmacies.filter((pharm: PharmacieEntreprise) => (!pharm?.email && !pharm?.gsm1) || (toWhatsApp && !pharm?.whatsapp))?.length > 0;

    if (hasMissingCredentials) {
      this.alertService.error(`Vous avez sélectionné des pharmacies avec des identifiants manquants. Veuillez choisir des pharmacies disposant ${toWhatsApp ? "d'un numéro de <b>Whatsapp</b> valide." : "d'un numéro de <b>téléphone</b> ou d'une <b>adresse e-mail</b> valide."}`, 'MODAL');
    } else {
      iif(
        () => toWhatsApp,
        this.federationSyndicatService.envoyerIdentifiantsParWhatsapp(this.selectedKeys),
        this.federationSyndicatService.envoyerIdentifiantsParMail(this.selectedKeys)
      ).subscribe(res => {
        this.alertService.mailSent(`Les identifiants ont été transmis avec succès aux pharmacies pour lesquelles ${toWhatsApp ? 'un numéro de WhatsApp' : 'un numéro de téléphone ou une adresse e-mail'} sont renseignés.`, 'Identifiants Envoyés');
      });
    }
  }


  activerOuDesactiverGroupe(): void {
    if (!this.readOnly && this.groupeId) {
      const statutEntreprise = this.f['statutEntreprise'].value;

      const action = statutEntreprise ? 'désactiver' : 'activer';
      this.userInputService
        .confirmAlt(
          'Confirmation',
          `Êtes vous sûr de vouloir ${action} ce groupe ?`
        )
        .then(
          () => {
            iif(
              () => statutEntreprise,
              this.federationSyndicatService.desactiverGroupeEntreprise(
                this.groupeId
              ),
              this.federationSyndicatService.activerGroupeEntreprise(
                this.groupeId
              )
            ).subscribe((_res) => {
              statutEntreprise
                ? this.f['statutEntreprise'].setValue(false)
                : this.f['statutEntreprise'].setValue(true);
            });
          },
          () => null
        );
    }
  }

  selectionChange(event: SelectionEvent): void {
    // if (this.authService.hasAnyAuthority(['ROLE_RESPONSABLE'])) {
    // Filter out rows that should not be selectable
    const filteredSelectedRows = event.selectedRows;
    const filteredDeselectedRows = event.deselectedRows;

    // Update selected keys by adding non-owner selected rows
    filteredSelectedRows.forEach(row => {
      if (!this.selectedKeys.includes(row.dataItem.id)) {
        this.selectedKeys.push(row.dataItem.id);
      }
    });

    // Update selected keys by removing non-owner deselected rows
    filteredDeselectedRows.forEach(row => {
      const index = this.selectedKeys.indexOf(row.dataItem.id);
      if (index > -1) {
        this.selectedKeys.splice(index, 1);
      }
    });

  }

  onCellClick(event: CellClickEvent): void {
    if (!this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']) || !this.readOnly || event.columnIndex === 0 || event?.column?.title === 'Role' || event?.column?.title === 'Statut Membre' || event?.column?.title === 'Action') {
      return;
    }

    const clickedItem = event.dataItem;
    if (!this.selectedKeys.includes(clickedItem.id)) {

      this.selectedKeys = [...this.selectedKeys, clickedItem.id];
    } else {
      const targetIndex = this.selectedKeys?.indexOf(clickedItem?.id);

      if (targetIndex > -1) {
        this.selectedKeys?.splice(targetIndex, 1);

        this.selectedKeys = [...this.selectedKeys];
      }
    }
  }

  openModal(content: TemplateRef<any>, forResponsableGroupe = true, size = 'xl'): void {
    this.isMembreModal = !forResponsableGroupe;
    this.hasMore = false;
    this.searchNavigation.skip = 0;
    this.modalService.open(content, { size, windowClass: 'custom-modal-width', modalDialogClass: 'fs-radius-modal' }).result.then(
      (_result) => this.viderFiltre(),
      (_reason) => this.viderFiltre()
    );
  }

  openModalIdentifiants(content: TemplateRef<any>, item?: PharmacieEntreprise, size = 'md') {
    this.modeEnvoiIdentifiants = null, this.targetPharmacie = item;
    this.modalService.open(content, { size, centered: true, modalDialogClass: 'fs-radius-modal' }).result.then(
      result => {
        this.targetPharmacie = null;
      },
      reason => {
        this.targetPharmacie = null;
      }
    );
  }

  sendWhatsappToUser(item: PharmacieEntreprise) {
    if (!item?.whatsapp) {
      this.alertService.identifiantsManquants("Le numéro WhatsApp de l'utilisateur sélectionné est inexistant.", 'Données Indisponible', item?.id);
    } else {
      this.federationSyndicatService.envoyerIdentifiantsParWhatsapp([item.id]).subscribe(res => {
        this.alertService.mailSent(`Un message avec login et mot de passe est envoyer à la pharmacie <b>${item?.raisonSociale}</b>`, 'Identifiants Envoyés');
      });
    }
  }

  sendCredentialsGeneric() {
    if (this.modeEnvoiIdentifiants === 'whatsapp') {
      this.targetPharmacie ? this.sendWhatsappToUser(this.targetPharmacie) : this.envoyerIdentifiantsMultiples(true);
    } else {
      this.targetPharmacie ? this.sendMailToUser(this.targetPharmacie) : this.envoyerIdentifiantsMultiples();
    }
  }

  hasUnsavedData(): boolean {
    return !this.readOnly && this.groupeForm?.dirty;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
