import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  Renderer2,
  TemplateRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  BlocOffre,
  CommandeType,
  ContactFournisseurGroupe,
  Offre,
  OffresService,
  SearchContactFournCriteria,
  TYPE_CMD,
} from '@wph/data-access';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Observable,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  finalize,
  iif,
  map,
  of,
  tap,
  takeUntil,
} from 'rxjs';
import { DeferredActionButtonsService, UserInputService } from '@wph/web/shared';
import { AuthService } from '@wph/core/auth';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { AlertService, DomainEnumeration, PlateformeService, SocieteType } from '@wph/shared';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { isEqual, cloneDeep } from 'lodash';
import { PharmacieEntrepriseCriteria } from '../../../models/pharmacie-entreprise-criteria.model';
import { GroupeEntreprise } from '../../../models/groupe-entreprise.model';
import {
  EnteteCommandeConsolideeMarche,
  FederationSyndicatService,
  FsCommandeCriteria,
  FsCommandesService,
  FsOffreService,
  PharmacieEntreprise,
} from '@wph/federation-syndicats';
import { Avis, AvisDTO, TypeAvis } from '../../../models/avis.model';
import { FsBLService } from '../../../services/fs-bl.service';
import { HttpErrorResponse } from '@angular/common/http';
import { SelectedPlateforme } from '@wph/web/layout';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { FEATURE_KEY } from '@wph/data-access';

@Component({
  selector: 'wph-edit-commande',
  templateUrl: './edit-commande.component.html',
  styleUrls: ['./edit-commande.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class EditCommandeComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  private cmdBrouillonClickListener: () => void;
  TYPE_CMD = TYPE_CMD;
  submitted: boolean;
  selectedOffre: Offre;
  initialSelectedOffreObj: Offre | null = null;
  commandeId: number;
  avisCmdId: number;
  validCommande = true;
  isLoading = false;
  readOnly = false;
  offre = false;
  printing: boolean;
  blobUrl: string;
  motifDeRefus: string = '';
  pageMode: string;
  assistantForm: FormGroup;
  offreForm: FormGroup;
  selectPack: Array<{ label: string; value: string }>;
  hasRoleAgentFournisseurOuCommercial: boolean = false;

  modePaiementValuePair: any[] = [{ label: 'Aucune', value: null }];
  transporteurValuePair: any[] = [{ label: 'Aucune', value: null }];

  laboEmailForm: FormGroup;

  isSupporteur: boolean;

  commandeType: CommandeType;

  offreId: number;

  cmdGroupeChangesApplied: boolean = false;

  commande: Offre;

  cmdConsolideeId: number;

  managedGroupe: GroupeEntreprise;
  membresDuGroupe: PharmacieEntreprise[];
  criteria: FsCommandeCriteria;

  commandeUnitaireExists: boolean = true;

  validCmdGroupe: boolean;

  filterResult: Offre[];
  cmdsUnitaires: Offre[];
  cmdsUnitairesBrouillon: Offre[];
  searchMember: FormControl = new FormControl();
  selectedMemberIds: number[] = [];
  feedbackSent = false;
  cmdGroupeCreated: boolean = false;

  reinitNbrCoffretCmd: boolean;

  avis: Avis = {
    commentaire: null,
    entCmdUnitaireMarcheId: null,
    estResponsable: false,
    groupeEntreprise: null,
    enteteCommandeConsolideeMarche: null,
    id: 0,
    laboratoire: 0,
    livraison: 0,
    paiement: 0,
    qualite: 0,
    raison: null,
    reduction: 0,
    sondeurEntreprise: null,
    typeAvis: TypeAvis.Positive,
  };
  monGroupe: any;
  membreId: number;
  membreDetails: any;
  isResponsable: boolean;
  canConsultBls: boolean;

  pdfViewerTitle: string;

  contactFournSubmitted: boolean;
  selectedContactFournIds: number[];
  currentPlateforme: SelectedPlateforme;
  contactFournisseurGroupeData: GridDataResult = { data: [], total: 0 };

  @ViewChild('rechercherProduitRef', { static: true }) rechercherProduitRef: any;
  @ViewChild('reactivationSaisie', { static: true }) reactivationSaisieModal: TemplateRef<any>;
  @ViewChild('resetModal', { static: true }) resetModal: TemplateRef<any>;
  @ViewChild('cmdsBrouillonModal') cmdsBrouillonModal: ElementRef;
  @ViewChild('refusalModal', { static: true }) refusalModal: TemplateRef<any>;
  @ViewChild('envoyerCommandeConsolideeWinGroupe', { static: true }) envoyerCommandeConsolideeWinGroupeModal: TemplateRef<any>;


  constructor(
    private fb: FormBuilder,
    private router: Router,
    private renderer: Renderer2,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private fsBlService: FsBLService,
    private alertService: AlertService,
    private authService: AuthService,
    private fsOffreService: FsOffreService,
    private offresService: OffresService,
    private commandeService: FsCommandesService,
    private plateformeService: PlateformeService,
    private userInputService: UserInputService,
    private fedSyndicatService: FederationSyndicatService,
    private deferredActionBtnService: DeferredActionButtonsService,
  ) {
    this.offreForm = this.fb.group({
      resetOption: ['O'],
      pack: [null],
    });

    this.currentPlateforme = this.plateformeService.getCurrentPlateforme();

    this.laboEmailForm = this.fb.group({ email: [''], confirmEmail: [''] });
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  get FEATURE_KEY() {
    return FEATURE_KEY;
  }

  ngOnInit() {
    this.isResponsable = this.authService.hasAnyAuthority(['ROLE_RESPONSABLE']);

    this.route.queryParams.subscribe(qParams => {
      this.readOnly = JSON.parse(qParams['readOnly']);
      this.offreId = +qParams['offreId'];
      this.cmdConsolideeId = +qParams['cmdConsolideeId'];

      if (qParams['modified']) {
        this.cmdGroupeChangesApplied = true;
        this.fetchCommandeById(this.commandeId);
      }
    });

    this.membreId = this.authService.getPrincipal()?.societe?.id;

    this.membreDetails = this.authService.getPrincipal();

    this.route.params.subscribe((params) => {
      this.commandeId = +params['id'];
      this.commandeType = params['type'];

      this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
        this.managedGroupe = myGroupe;
        this.initializeAvis();

        this.fetchCommandeById(this.commandeId);
      });

      if (this.commandeType) {
        switch (this.commandeType) {
          case TYPE_CMD.GROUPE:
            this.pdfViewerTitle = 'Imprimer Commande Consolidée';
            break;
          case TYPE_CMD.INDIVIDUELLE:
            this.pdfViewerTitle = 'Imprimer Commande Individuelle';
            break;
          case TYPE_CMD.UNITAIRE:
            this.pdfViewerTitle = 'Imprimer Commande Unitaire';
            break;
          default:
            this.pdfViewerTitle = 'Imprimer Commande';
            break;
        }
      }

      this.listenToSearchMemberChanges();
    });

    this.listenToValidCommandeChanges();
  }

  private listenToValidCommandeChanges() {
    this.offresService.getValidCommandeObservable().subscribe((isValid) => {
      if (typeof isValid === 'boolean') {
        this.checkifvalid();
      }
    });
  }

  private initializeAvis(entCmdUnitaireMarcheId?: number) {
    this.avis = {
      commentaire: null,
      estResponsable: this.isResponsable,
      id: null,
      typeAvis: TypeAvis.Positive,
      entCmdUnitaireMarcheId:
        this.commandeType === TYPE_CMD.UNITAIRE
          ? entCmdUnitaireMarcheId || this.commandeId
          : null,
      enteteCommandeConsolideeMarche:
        this.commandeType === TYPE_CMD.GROUPE
          ? ({ id: this.commandeId } as EnteteCommandeConsolideeMarche)
          : null,
      groupeEntreprise: this.membreDetails?.groupe ?? null,
      sondeurEntreprise: {
        id: this.membreDetails?.societe?.id,
      } as PharmacieEntreprise,
      raison: null,
      laboratoire: 0,
      livraison: 0,
      paiement: 0,
      qualite: 0,
      reduction: 0,
    };
  }

  private checkIfAvisExists(cmdConsolideeId?: number): Observable<boolean> {
    const sondeurId = this.authService.getPrincipal()?.societe?.id;
    if (!sondeurId) {
      console.error('sondeurId is null');
      return of(false);
    }

    return this.commandeService
      .getBySondeurIdOffreIdCmdId(sondeurId, cmdConsolideeId)
      .pipe(
        map((avis: AvisDTO | AvisDTO[]) => {
          // Check if the response is an array
          if (Array.isArray(avis)) {
            return avis.length > 0;
          }
          // Check if the response is a single object
          return avis !== null;
        }),
        catchError((error) => {
          console.error('Error fetching avis:', error);
          return of(false);
        })
      );
  }

  @ViewChild('satisfactionModal', { static: true })
  satisfactionModal: TemplateRef<any>;

  openSatisfactionModal(modalContent: TemplateRef<any>) {
    if (this.feedbackSent) {
      this.alertService.error(
        `Un avis a déjà été envoyé pour cette commande.`,
        'MODAL'
      );
      return;
    }

    this.modalService.open(modalContent, { centered: true });
  }

  satisfactionOffre() {
    this.openSatisfactionModal(this.satisfactionModal);
  }

  soumettreSatisfactionModal(): void {
    if (this.feedbackSent) {
      this.alertService.error(`Un avis a déjà été envoyé pour cette commande.`, 'MODAL');
      return;
    }
    this.userInputService
      .confirmAlt('Confirmation', `Êtes vous sûr de vouloir envoyer cet avis ?`)
      .then(
        () => {
          this.commandeService.sauvegarderAvis(this.avis).subscribe(
            {
              next: () => {
                this.feedbackSent = true;
                this.alertService.successAlt(`Votre avis a été envoyé avec succès.`, 'Avis envoyé', 'MODAL');
              },
              error: (error) => {
                const errorMessage = error?.error?.message || "Erreur lors de l'enregistrement de l'avis.";
                this.alertService.error(errorMessage, 'MODAL');
                console.error('Error saving avis:', error);
              }
            }
          );

          this.modalService.dismissAll();
        },
        () => null
      );
  }

  fetchCommandeById(commandeId: number, cmdGroupeChangesApplied = false): void {
    this.cmdGroupeChangesApplied = cmdGroupeChangesApplied;

    if (this.commandeType === TYPE_CMD.GROUPE || this.commandeType === TYPE_CMD.UNITAIRE) {
      if (commandeId) {
        iif(
          () => this.commandeType === TYPE_CMD.GROUPE,
          this.commandeService.buildCommandeConsolidee(commandeId),
          this.commandeService.buildCommandeUnitaireAsOffre(commandeId)
        ).subscribe((res) => {
          if (this.commandeType === TYPE_CMD.GROUPE) {
            this.initCommandeGroupe(res);
          } else {
            this.initCommandeUnitaire(res);
          }

          this.checkIfCommandeUnitaireExists();

          this.setPackTitles();
          this.checkCommandeStateAndForceSetReadOnly(this.selectedOffre);

          this.setSousBlocCollapseState(true);
          this.setConditionsEtRemisesDisplayState(true);

          this.pushMobileMenuOptions();
        });
      } else {
        this.commandeService
          .buildCommandeConsolidee(this.cmdConsolideeId)
          .subscribe((res) => {
            this.commande = res;
            this.selectedOffre = this.commande;

            if (this.commandeType === TYPE_CMD.GROUPE) {
              this.offresService.initialiserEtatOffre(this.selectedOffre as Offre);
            } else {
              this.offresService.initialiserEtatOffreUnitaire(this.selectedOffre as Offre);
              this.offresService.reinitialiserEtatCommandeUnitaire(this.selectedOffre);
              setTimeout(() => {
                this.initialSelectedOffreObj = cloneDeep(this.selectedOffre);
              }, 200);
            }

            this.setPackTitles();
            this.checkCommandeStateAndForceSetReadOnly(this.selectedOffre);

            this.setSousBlocCollapseState(true);
            this.setConditionsEtRemisesDisplayState(true);

            this.pushMobileMenuOptions();
          });
      }
    } else { // ? Gérer le cas d'une commande individuelle
      if (!this.commandeId) {
        const incrementViewCount = history.state?.['incr'] || false;
        
        this.offresService.getOffreById(this.offreId, incrementViewCount).subscribe(offre => {
          this.initCommandeIndividuelle(offre);

          this.setSousBlocCollapseState(true);
          this.setConditionsEtRemisesDisplayState(true);

          this.pushMobileMenuOptions();
        });
      } else {
        this.offresService.getCommandesById(this.commandeId).subscribe(offre => {
          this.initCommandeIndividuelle(offre);
          this.checkCommandeStateAndForceSetReadOnly(this.selectedOffre);

          this.setSousBlocCollapseState(true);
          this.setConditionsEtRemisesDisplayState(true);

          this.pushMobileMenuOptions();
        });
      }

    }
  }

  setConditionsEtRemisesDisplayState(show: boolean) {
    this.offresService.setConditionsAndPaliersRowDisplayState(this.selectedOffre, show);
  }

  setSousBlocCollapseState(collapse: boolean) {
    this.offresService.setCollapseStateOnOffre(this.selectedOffre, collapse);
  }

  setModePaiementAndTransporteurValuePair(): void {
    this.modePaiementValuePair = [{ label: 'Aucune', value: null }];

    if (this.selectedOffre.listDelaiPaiements && this.selectedOffre.listDelaiPaiements.length) {
      this.selectedOffre.listDelaiPaiements?.map(item => {
        if (this.selectedOffre.delaiPaiement && (item.id === this.selectedOffre?.delaiPaiement?.id)) {
          this.modePaiementValuePair.push({ label: item.label, value: this.selectedOffre.delaiPaiement });
        } else {
          this.modePaiementValuePair.push({ label: item.label, value: item });
        }
      });
    }

    this.transporteurValuePair = [{ label: 'Aucun', value: null }];

    if (this.selectedOffre.raisonSocialeTransporteur) {
      this.selectedOffre?.raisonSocialeTransporteur?.split(',').map(transporteur => {
        const transporteurCmd = transporteur.trim();
        this.transporteurValuePair.push({ label: transporteurCmd, value: transporteurCmd });
      });
    }
  }

  fireModePaiementValueChange(): void {
    for (let bloc of this.selectedOffre?.listeBlocs) {
      this.offresService.refreshEtatBlocOffre(bloc);
      this.offresService.palierOffreTraitement(this.offresService.getOffreRacine(bloc));

      this.checkifBlocIsValid(bloc);
    }
  }

  checkifBlocIsValid(currentbloc: BlocOffre) {
    const currentstat = currentbloc.parent;

    if ((currentstat && currentstat.etat === 'I')) {
      this.validCommande = false;
    } else {
      this.validCommande = true;
    }

    this.offresService.subject.next(this.validCommande)
  }

  initCommandeGroupe(commande: Offre): void {
    this.commande = commande;
    this.selectedOffre = this.commande;

    this.commande.supporterEntrepriseTemp = commande?.supporterEntreprise;
    this.selectedOffre.supporterEntrepriseTemp = commande?.supporterEntreprise;

    this.isSupporteur = this.checkIsSupporteur(
      this.commande?.supporterEntreprise
    );

    this.setModePaiementAndTransporteurValuePair();

    this.getCommandeUnitaires();
    this.forceExpandConditionsPaliersRow();

    this.offresService.initialiserEtatOffre(this.selectedOffre as Offre);
    this.offresService.palierOffreTraitement(this.selectedOffre as Offre);

    this.checkifvalid();
  }

  initCommandeUnitaire(commande: Offre): void {
    this.commande = commande;
    this.selectedOffre = this.commande;

    this.fsBlService.getBLsUnitaireByCommandeId({ cmdUnitaireId: this.commande.enteteCommandeId }).subscribe(res => {
      this.canConsultBls = !!res?.length;
    });

    this.offresService.initialiserEtatOffreUnitaire(
      this.selectedOffre as Offre
    );
    this.checkifvalid();

    this.forceExpandConditionsPaliersRow();


    setTimeout(() => {
      this.initialSelectedOffreObj = cloneDeep(this.selectedOffre);
    }, 200);
  }

  initCommandeIndividuelle(commande: Offre): void {
    this.commande = commande;
    this.selectedOffre = this.commande;

    this.offresService.initialiserEtatOffre(this.selectedOffre);
    this.offresService.palierOffreTraitement(this.selectedOffre);

    this.setModePaiementAndTransporteurValuePair();

    this.setPackTitles();

    this.forceExpandConditionsPaliersRow();

    setTimeout(() => {
      this.initialSelectedOffreObj = cloneDeep(this.selectedOffre);
    }, 200);
  }

  forceExpandConditionsPaliersRow(): void {
    this.offresService.setConditionsAndPaliersRowDisplayState(this.selectedOffre, true);
  }

  checkCommandeStateAndForceSetReadOnly(cmdAsOffre: Offre) {
    if (
      ((this.commandeType === TYPE_CMD.GROUPE || this.commandeType === TYPE_CMD.UNITAIRE) && cmdAsOffre?.etatCommandeAchatGroupe !== 'BROUILLON' && cmdAsOffre?.etatCommandeAchatGroupe !== 'ACCEPTEE') ||
      (this.commandeType === TYPE_CMD.INDIVIDUELLE && cmdAsOffre?.commandStatut !== 'BROUILLON')
    ) {
      const qParams = this.route.snapshot.queryParams;
      this.readOnly = true;
      this.router.navigate([], { queryParams: { ...qParams, readOnly: this.readOnly } });
    }
  }

  checkIsSupporteur(supporteur: PharmacieEntreprise): boolean {
    if (supporteur) {
      return this.authService.getPrincipal().societe?.id === supporteur?.id;
    }
    return false;
  }

  consulterBLS() {
    if (this.commandeType === TYPE_CMD.GROUPE) {
      this.router.navigateByUrl(`/achats-groupes/bons-livraison/commande/${this.commande.enteteCommandeId}`);

    }
    else if (this.commandeType === TYPE_CMD.INDIVIDUELLE) {
      this.router.navigateByUrl(`/achats-groupes/bons-livraison/commande/${this.commande.enteteCommandeId}/individuelle`);
    }

    else {
      this.router.navigateByUrl(`/achats-groupes/bons-livraison/commande/${this.commande.enteteCommandeId}/unitaire`);
    }
  }

  checkIfCommandeUnitaireExists(): void {
    if (this.managedGroupe &&
      (this.selectedOffre?.etatCommandeAchatGroupe !== 'REFUSEE' && this.selectedOffre?.etatCommandeAchatGroupe !== 'EN_ATTENTE')
    ) {
      this.commandeService
        .getCommandeUnitaireByMembre(
          this.authService.getPrincipal()?.societe?.id,
          this.selectedOffre?.enteteCommandeId
        )
        .subscribe((res) => {
          this.commandeUnitaireExists = !!res;
        });
    }
  }

  getCommandeUnitaires() {
    if (this.selectedOffre?.etatCommandeAchatGroupe !== 'REFUSEE' && this.selectedOffre?.etatCommandeAchatGroupe !== 'EN_ATTENTE') {
      this.commandeService
        .getCommandeUnitaireByCmdConsolideeId(this.commande?.enteteCommandeId)
        .subscribe((res) => {
          this.filterResult = res;
          this.cmdsUnitaires = res
            .filter(
              (cmd) =>
                cmd.etatCommandeAchatGroupe !== 'BROUILLON' &&
                cmd.etatCommandeAchatGroupe !== 'ANNULEE'
            )
            .sort((a, b) =>
              a.client.nomResponsable.localeCompare(b.client.nomResponsable)
            );
          this.cmdsUnitairesBrouillon = res.filter(
            (cmd) => cmd.etatCommandeAchatGroupe === 'BROUILLON'
          );
        });
    }
  }

  activerLaSaisie(): void {
    if (this.selectedOffre.distributeur && this.selectedOffre.supporterEntreprise) {
      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir activer la saisie sur cette offre pour les membres de votre groupe ?`).then(
        () => {
          let conditionsBlocOffreCmd = this.offresService.fetchAllConditionUnitInAllInnerBlocOffres(this.selectedOffre);
          if (conditionsBlocOffreCmd?.length) {
            const selectedConditions = conditionsBlocOffreCmd;
            this.fsOffreService.saveConditionsCommandeConsolidee(this.selectedOffre?.enteteCommandeId, selectedConditions)
              .subscribe(res => this.attachementRequestChain(true));
          } else {
            this.attachementRequestChain(true);
          }
        },
        () => null
      );
    } else {
      this.alertService.error(`Merci de compléter les informations suivant: <ul>
        ${(!this.selectedOffre.distributeur && this.selectedOffre?.distributeurs?.length > 1) ? "<li>Sélectionner un distributeur</li>" : ""}
        ${!this.selectedOffre.supporterEntreprise ? "<li>Sélectionner un supporteur</li>" : ""}
        </ul>`, 'MODAL');
    }
  }

  attachementRequestChain(activerSaisie = false): void {
    this.commandeService.attacherSupporteur(this.selectedOffre.enteteCommandeId, this.selectedOffre?.supporterEntreprise?.id).subscribe(res => {
      this.fsOffreService.attacherDistributeurCommandeConsolidee(this.selectedOffre?.enteteCommandeId, this.selectedOffre?.distributeur?.id).subscribe(res => {
        if (activerSaisie) {
          this.fsOffreService.accepterFsOffre(this.selectedOffre?.enteteCommandeId).subscribe((res: Offre) => {
            this.cmdGroupeCreated = true;
            this.fetchCommandeById(this.commandeId);
          });
        } else {
          this.cmdGroupeCreated = true;
          this.fetchCommandeById(this.commandeId);
        }
      })
    });
  }

  toggleReason(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.avis.raison = inputElement.value;
  }


  soumettreRefuserOffre(): void {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir réfuser cette offre ?`).then(
      () => {
        this.avis.id = null;
        this.commandeService.sauvegarderAvis(this.avis).subscribe({
          next: () => {
            const offreTitle = this.selectedOffre?.titre || 'Offre';
            this.feedbackSent = true;
            this.fsOffreService.refuserFsOffre(this.selectedOffre?.enteteCommandeId).subscribe(res => {
              this.alertService.successAlt(`L'offre "${offreTitle}" a été réfusée avec succès.`, 'Offre Réfusé ', 'MODAL');

              this.back(true);
            });
          },
          error: (error) => {
            const errorMessage = error?.error?.message || 'Erreur lors de l\'enregistrement de l\'avis.';
            this.alertService.error(errorMessage, 'MODAL');
            console.error('Error saving avis:', error);
          }
        }
        );
        this.modalService.dismissAll();
      },
      () => null
    );
  }

  refuserOffre() {
    if (this.selectedOffre?.etatCommandeAchatGroupe === 'REFUSEE') {
      this.alertService.error('Cette offre a déjà été refusée.', 'MODAL');
      return;
    }

    if (this.feedbackSent) {
      this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
      return;
    }

    this.checkIfAvisExists(this.selectedOffre?.enteteCommandeId).subscribe({
      next: (avisExists) => {
        if (avisExists) {
          this.alertService.error('Un avis a déjà été envoyé pour cette offre.', 'MODAL');
        } else {
          this.openRefusalModal(this.refusalModal);
        }
      },
      error: (error) => {
        this.alertService.error('Erreur lors de la vérification de l\'avis.', 'MODAL');
        console.error('Erreur API:', error);
      }
    });

  }

  openRefusalModal(modalContent: TemplateRef<any>) {
    this.modalService.open(modalContent, { centered: true });
  }

  fetchMembreOfCurrentGroupe(): void {
    const criteria = new PharmacieEntrepriseCriteria({
      groupeEntrepriseDTO: this.managedGroupe,
      typeEntreprises: [SocieteType.CLIENT],
    });
    this.fedSyndicatService
      .searchPharmacieEntreprise({ skip: 0 }, criteria)
      .subscribe((res) => {
        this.membresDuGroupe = res.content;
      });
  }

  listenToSearchMemberChanges(): void {
    this.searchMember.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(200),
        distinctUntilChanged()
      )
      .subscribe((term) => {
        this.criteria = new FsCommandeCriteria({
          offre: this.selectedOffre,
          nomCompletClient: term,
        });

        this.searchCommandeUnitaire(this.criteria);
      });
  }

  searchCommandeUnitaire(criteria: FsCommandeCriteria): void {
    this.commandeService
      .searchCommandesUnitaires({ skip: 0 }, criteria)
      .subscribe((res) => {
        this.filterResult = res?.content as any;
      });
  }

  markMemberAsSelected(id: number): void {
    if (!this.selectedMemberIds?.includes(id)) {
      this.selectedMemberIds.push(id);
    } else {
      const index = this.selectedMemberIds.findIndex((item) => item === id);

      this.selectedMemberIds?.splice(index, 1);
    }
  }

  selectAll(all: boolean) {
    if (all) {
      this.selectedMemberIds = this.filterResult?.map(
        (res) => res?.enteteCommandeId
      );
    } else {
      this.selectedMemberIds = [];
    }
  }

  setPackTitles(): void {
    this.selectPack = this.selectedOffre?.listeBlocs?.map((bloc) => {
      return { label: bloc.titre, value: bloc.id.toString() };
    });
  }

  applyOffreReset(): void {
    const values = this.offreForm.value;

    if (values['resetOption'] === 'O') {
      this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir réinitialiser la commande ?`).then(
        () => {
          if (this.commandeType === TYPE_CMD.UNITAIRE) {
            this.offresService.reinitialiserEtatCommandeUnitaire(this.selectedOffre);
          } else {
            this.offresService.reinitialiserEtatCommande(this.selectedOffre);
          }
          this.reinitNbrCoffretCmd = this.selectedOffre?.coffretEnabled && true;

          this.modalService.dismissAll();
        },
        () => null
      );
    } else if (values['resetOption'] === 'P') {
      const targetIndex = this.selectedOffre?.listeBlocs?.findIndex(bloc => bloc?.id === +values['pack']);

      if (targetIndex > -1) {
        this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir réinitialiser le pack: <b>${this.selectedOffre.listeBlocs[targetIndex]?.titre}</b> sélectionné ?`).then(
          () => {
            if (this.commandeType === TYPE_CMD.UNITAIRE) {
              this.offresService.reinitialiserEtatCommandeBlocOffreSurCmdUnit(this.selectedOffre.listeBlocs[targetIndex]);
            } else {
              this.offresService.reinitialiserEtatCommandeBlocOffre(this.selectedOffre.listeBlocs[targetIndex]);
            }

            this.reinitNbrCoffretCmd = this.selectedOffre?.coffretEnabled && true;

            this.modalService.dismissAll();
          },
          () => null
        );
      }
    }
  }

  get resetCtrls() {
    return this.offreForm.controls;
  }

  public checkifvalid() {
    if (
      this.selectedOffre?.listeBlocs &&
      this.selectedOffre?.listeBlocs?.length &&
      this.selectedOffre?.listeBlocs?.length > 0
    ) {
      for (const iterator of this.selectedOffre.listeBlocs) {
        if (iterator.etat === 'I' || ((this.commandeType !== TYPE_CMD.UNITAIRE) && (this.selectedOffre?.etatCmdOffre === 'I'))) {
          this.validCommande = false;
          this.validCmdGroupe = false;
          break;
        }
        this.validCommande = true;
        this.validCmdGroupe = true;
      }

    }
  }

  enregistrerCommande(): void {
    if (this.validCommande) {
      this.isLoading = true;

      const payload: Offre = {
        ...this.selectedOffre,
        client: this.authService.getPrincipal()?.societe,
        enteteCommandeId: this.commandeId ? this.selectedOffre.enteteCommandeId : null,
      };

      this.commandeService
        .saveCommandeUnitaire(payload as Offre, this.cmdConsolideeId)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe((res) => {
          this.back(true);
          this.alertService.successAlt('La commande unitaire a été enregistrée avec succès.', 'Commande Enregistrée', 'MODAL');
        });
    }
  }

  enregistrerCommandeIndividuelle(): void {
    if (this.validCommande && this.selectedOffre?.distributeur) {
      this.isLoading = true;

      this.selectedOffre.client = this.authService.getPrincipal()?.societe;

      for (let bloc of this.selectedOffre?.listeBlocs) {
        this.clearDetailCommandeId(bloc);
      }

      this.offresService.createCommande(this.selectedOffre)
        .pipe(finalize(() => this.isLoading = false))
        .subscribe(res => {
          this.submitted = true, this.back(true);
          this.alertService.successAlt('La commande individuelle a été enregistrée avec succès.', 'Commande Enregistrée', 'MODAL');
        });
    } else {
      this.alertService.error(`Veuillez sélectionner un distributeur pour cette commande.`, 'MODAL');
    }
  }

  clearDetailCommandeId(blocOffre: BlocOffre) {
    if (!blocOffre) {
      return;
    }

    for (let fils of blocOffre?.listeFils) {
      if (fils?.typeBloc === 'F') {
        fils['detailCommandeId'] = null;
      }

      this.clearDetailCommandeId(fils);
    }
  }

  envoyerCommandeIndividuelle(): void {
    if (this.laboEmailForm.invalid) {
      this.alertService.error('Veuillez verifier les champs obligatoires', 'MODAL');
      return;
    }

    this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir envoyer cette commande au distributeur: <b>${this.selectedOffre?.distributeur?.raisonSociale}</b> ?`).then(
      () => {
        const { email } = this.laboEmailForm.value;

        this.selectedOffre.client = this.authService.getPrincipal()?.societe;

        this.offresService.createCommande(this.selectedOffre)
          .subscribe(res => {
            this.commande['enteteCommandeId'] = res?.id;

            this.commandeService.envoyerCommandeIndividuelle(this.commande?.enteteCommandeId, email).subscribe(res => {
              this.modalService.dismissAll();

              this.back(true);
              this.alertService.successAlt('La commande individuelle a été envoyée avec succès.', 'Commande Envoyée', 'MODAL');
            });
          });
      },
      () => null
    )
  }

  enregistrerEtEnvoyerCommandeIndividuelle(): void {
    const principal = this.authService.getPrincipal();

    if (!principal?.societe?.numIce) {
      this.alertService.error(`Vous n'avez pas encore spécifié votre numéro ICE, veuillez compléter votre profil.`, 'MODAL');
    } else {
      if (!this.validCommande || !this.selectedOffre?.distributeur ||
        (this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) ||
        (this.selectedOffre?.raisonSocialeTransporteur?.length && !this.selectedOffre?.transporteurCommande)
      ) {
        this.verifierElementsObligatoires();
      } else {
        this.envoyerCommandeGeneric();
      }
    }
  }

  verifierElementsObligatoires(): void {
    this.alertService.error(`Veuillez compléter les éléments obligatoires suivants: 
      <ul class="cstm-ul">
      ${!this.selectedOffre?.distributeur ? "<li>Sélectionner un Distributeur</li>" : ''}
      ${(this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) ? "<li>Sélectionner un <b>Mode de paiement</b></li>" : ''}
      ${(this.selectedOffre?.raisonSocialeTransporteur?.length && !this.selectedOffre?.transporteurCommande) ? "<li>Sélectionner un <b>Transporteur</b></li>" : ''}
      </ul>
      `, 'MODAL');
  }

  validerCommandeIndividuelle() {
    this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir envoyer cette commande ?').then(
      (result) => {
        result && this.offresService.valdierCommandeById(this.selectedOffre.enteteCommandeId).subscribe(() => {
          this.back(true);
          this.alertService.successAlt('La commande individuelle a été envoyée avec succès.', 'Commande Envoyée', 'MODAL');
        });

      }, () => null);
  }

  annulerCommandeIndividuelle() {
    this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir annuler cette commande ?').then(
      (result) => {
        this.offresService.annulerCommandeById(this.selectedOffre.enteteCommandeId)
          .subscribe(() => {
            this.back(true);
            this.alertService.successAlt(
              `La commande individuelle a été annulée avec succès.`,
              `Commande Annulée`, 'MODAL'
            );
          });
      }, () => null);
  }

  supprimerCommandeIndividuelle(): void {
    this.userInputService.confirmAlt('Confirmation', 'Êtes vous sûr de vouloir supprimer cette commande ?').then(
      (result) => {
        this.offresService.supprimerCommandeById(this.selectedOffre.enteteCommandeId)
          .subscribe(() => {
            this.back(true);
            this.alertService.successAlt(
              `La commande individuelle a été supprimée avec succès.`,
              `Commande Supprimée`, 'MODAL'
            );
          })
      },
      () => null
    );
  }

  getParentBlocId(blocOffre: BlocOffre, codeProduit: string) {
    for (let fils of blocOffre?.listeFils) {
      if (fils.codeProduitCatalogue === codeProduit && fils.qteCmd > 0) {
        return true;
      }

      if (fils?.listeFils.length > 0) {
        return this.getParentBlocId(fils, codeProduit);
      }
    }

    return false;
  }

  envoyerCommandeGeneric(skipRequiredFieldsCheck = true) {
    const principal = this.authService.getPrincipal();

    if (!principal?.societe?.numIce) {
      this.alertService.error(`Vous n'avez pas encore spécifié votre numéro ICE, veuillez compléter votre profil.`, 'MODAL');
    } else {
      if (skipRequiredFieldsCheck) {
        this.laboEmailForm.setValue({
          email: this.selectedOffre?.distributeur?.email,
          confirmEmail: this.selectedOffre?.distributeur?.email
        });

        if (this.commandeType === TYPE_CMD.GROUPE) {
          this.envoyerCommande();
        } else if (this.commandeType === TYPE_CMD.INDIVIDUELLE) {
          if (this.selectedOffre?.distributeur?.email) {
            this.envoyerCommandeIndividuelle();
          } else {
            this.alertService.error(`L'adresse e-mail du distributeur sélectionné pour votre commande est actuellement indisponible. Merci de contacter le responsable de <b>${this.plateformeService.getPlateformeName()}</b>`, 'MODAL');
          }
        }
      } else {
        this.verifierElementsObligatoires();
      }
    }
  }

  shouldSkipRequiredFieldsCheck(): boolean {
    return !(
      !this.selectedOffre?.distributeur ||
      (this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) ||
      (this.selectedOffre?.raisonSocialeTransporteur?.length && !this.selectedOffre?.transporteurCommande)
    );
  }

  canPrintCmdConsolidee() {
    return (this.isSupporteur || this.isResponsable) &&
      (
        this.commande?.etatCommandeAchatGroupe === 'VALIDEE' ||
        this.commande?.etatCommandeAchatGroupe === 'ENVOYEE' ||
        this.commande?.etatCommandeAchatGroupe === 'LIVREE' ||
        this.commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON'
      );
  }

  fieldError(field: string, error: string) {
    return (
      this.laboEmailForm.get(field).touched &&
      this.laboEmailForm.get(field).hasError(error)
    );
  }

  isFieldInvalid(field: string) {
    return (
      this.laboEmailForm.get(field).invalid &&
      this.laboEmailForm.get(field).touched
    );
  }

  EmailEqualtyValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const mainEmail = this.laboEmailForm?.get('email').value;
      if (mainEmail === control.value) {
        return null;
      }
      return { diff: true, email: false };
    };
  }

  envoyerCommandeUnitaire() {
    this.userInputService.confirmAlt('Confirmation', `Êtes vous sûr de vouloir transmettre la commande avec <b>code commande: ${(this.commande as Offre)?.codeCommande}</b> ?`)
      .then(
        () => {
          const enteteCommandeId = (this.commande as Offre)?.enteteCommandeId;
          this.commandeService.transmettreCommandeUnitaire(enteteCommandeId, this.managedGroupe?.id).subscribe({
            next: (_res) => {
              this.back();
              this.alertService.successAlt(`La commande a été envoyée avec succès.`, 'Commande Envoyée', 'MODAL');
              // Close all related modals before showing satisfaction modal
              // Use a timeout to ensure all modals are closed before opening the satisfaction modal
              setTimeout(() => {
                if (this.modalService.hasOpenModals()) {
                  this.modalService.dismissAll();
                }
                this.checkIfAvisExists((this.commande as Offre)?.enteteCommandeId).subscribe(
                  (avisExists) => {
                    if (!avisExists && !this.isResponsable) {
                      this.openSatisfactionModal(this.satisfactionModal);
                    }
                  }
                );
              }, 3500);
            },
            error: (err) => {
              throw new HttpErrorResponse(err);
            }
          });
        },
        () => null
      );
  }

  private envoyerCommande() {
    this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir envoyer cette commande au distributeur: <b>${this.selectedOffre?.distributeur?.raisonSociale}</b> ?`).then(
      () => this.multiStepContactFournisseurValidation(),
      () => null
    );
  }

  private multiStepContactFournisseurValidation(): void {
    this.checkExistingContactFournisser()
      .pipe(
        map(contactsFourn => {
          const contactFournData = contactsFourn.content || [];
          this.contactFournisseurGroupeData = { data: contactFournData, total: contactFournData.length };

          return contactFournData;
        }),
        tap((contactsFourn: ContactFournisseurGroupe[]) => {
          if (!contactsFourn?.length) {
            const emailFournisseur: string = this.selectedOffre?.distributeur?.email;

            if (emailFournisseur) {
              this.goEnvoyerCmdConsolidee(emailFournisseur);
            } else {
              if (this.plateformeService.isPlateForme('WIN_GROUPE')) {
                this.alertService.error(`Aucun contact fournisseur n'a été trouvé pour le distributeur sélectionné : <b>${this.selectedOffre?.distributeur?.raisonSociale}</b>. 
                  Merci de bien vouloir le renseigner en suivant <a class="modal-link-label" href="achats-groupes/gestion-contact-fournisseurs/edit?readOnly=false">ce lien</a>.`, 'MODAL');
              } else {
                this.alertService.error(`L'adresse e-mail du distributeur sélectionné pour votre commande est actuellement indisponible. Merci de contacter le responsable de <b>${this.plateformeService.getPlateformeName()}</b>`, 'MODAL');
              }
            }
          } else {
            this.openEnvoyerCmdConsolideeWinGroupeModal();
          }
        })
      ).subscribe();
  }

  private checkExistingContactFournisser() {
    const criteria: SearchContactFournCriteria = new SearchContactFournCriteria({
      groupe: this.monGroupe,
      fournisseur: this.selectedOffre?.distributeur as any
    });

    return this.fedSyndicatService.searchContactsFournisseurGroupe({ pageSize: 20, skip: 0 }, criteria);
  }

  private goEnvoyerCmdConsolidee(emailFournisseur: string): void {
    this.commandeService.envoyerCommandeConsolidee(
      (this.commande as Offre)?.enteteCommandeId,
      this.managedGroupe?.id, emailFournisseur
    ).subscribe((_res) => {
      this.alertService.successAlt(`La commande a été envoyée avec succès.`, 'Commande Envoyée', 'MODAL');

      this.fetchCommandeById(this.commandeId || this.selectedOffre.enteteCommandeId);

      // Use a timeout to ensure the success alert is shown before opening the satisfaction modal
      setTimeout(() => {
        // Check if an avis already exists after the success modal is visible for 3.5 seconds
        this.checkIfAvisExists((this.commande as Offre)?.enteteCommandeId).subscribe((avisExists) => {
          console.log((this.commande as Offre)?.id);
          console.log('Envoyer cmd');
          console.log('Avis exists:', avisExists);
          if (avisExists) {
            this.alertService.error(`Un avis a déjà été envoyé pour cette commande.`, 'MODAL');
          } else {
            this.satisfactionOffre();
          }
        });
      }, 3500); // Wait for 3.5 seconds before checking and showing the satisfaction modal
    });
  }

  private openEnvoyerCmdConsolideeWinGroupeModal(): void {
    this.modalService.open(
      this.envoyerCommandeConsolideeWinGroupeModal,
      { centered: true, size: 'lg', modalDialogClass: 'fs-radius-modal' }
    ).result.then(
      () => this.clearContactFournisseurData(),
      () => this.clearContactFournisseurData()
    );
  }

  validerContactFournisseurSelectionne(modal: NgbModalRef): void {
    this.contactFournSubmitted = true;
    if (this.selectedContactFournIds?.length) {
      const emailFournisseur = (this.contactFournisseurGroupeData.data as ContactFournisseurGroupe[])
        .filter(item => this.selectedContactFournIds.includes(item.id))
        .map(item => item?.emailFournisseur)
        .join(';');

      modal.close();
      this.goEnvoyerCmdConsolidee(emailFournisseur);
    }
  }

  private clearContactFournisseurData(): void {
    this.selectedContactFournIds = [];
    this.contactFournisseurGroupeData = { data: [], total: 0 };
  }

  enregistrerEtEnvoyerCommande() {
    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir envoyer cette commande pour l'offre <b>${this.selectedOffre.titre.toLocaleUpperCase()}</b> ?`
      )
      .then(
        () => {
          if (this.validCommande) {
            this.isLoading = true;

            const payload: Offre = {
              ...this.selectedOffre,
              client: this.authService.getPrincipal()?.societe,
              enteteCommandeId: this.commandeId ? this.selectedOffre.enteteCommandeId : null,
            };

            this.commandeService
              .saveCommandeUnitaire(payload as Offre, this.cmdConsolideeId)
              .pipe(finalize(() => this.isLoading = false))
              .subscribe({
                next: (res) => {
                  const entCmdUnitaireMarcheId = res?.id;
                  console.log('Saved Commande ID:', entCmdUnitaireMarcheId);

                  if (entCmdUnitaireMarcheId) {
                    this.isLoading = true;
                    this.initializeAvis(entCmdUnitaireMarcheId);

                    this.commandeService
                      .transmettreCommandeUnitaire(
                        entCmdUnitaireMarcheId,
                        this.managedGroupe?.id
                      )
                      .pipe(finalize(() => this.isLoading = false))
                      .subscribe((_res) => {
                        this.back();
                        this.alertService.successAlt(
                          `La commande a été envoyée avec succès.`,
                          'Commande Envoyée',
                          'MODAL'
                        );
                        // Close all related modals before showing satisfaction modal
                        // Use a timeout to ensure all modals are closed before opening the satisfaction modal
                        setTimeout(() => {
                          if (this.modalService.hasOpenModals()) {
                            this.modalService.dismissAll();
                          }
                          this.checkIfAvisExists((this.commande as Offre)?.enteteCommandeId).subscribe((avisExists) => {
                            console.log(' enregistrerEtEnvoyerCommande');
                            console.log('Avis exists:', avisExists);
                            if (avisExists || this.isResponsable) {
                              return;
                            } else {
                              this.openSatisfactionModal(
                                this.satisfactionModal
                              );
                            }
                          });
                        }, 3500);
                      });
                  } else {
                    this.alertService.error('Erreur lors de la sauvegarde de la commande.', 'MODAL');
                  }
                },
                error: (error) => {
                  this.alertService.error('Erreur lors de la sauvegarde de la commande.', 'MODAL');
                  console.error('Error saving commande:', error);
                }
              });
          }
        },
        () => null
      );
  }

  // ? Valider et Envoyer Commande Consolidée
  validerCommandeConsolidee() {
    const principal = this.authService.getPrincipal();

    if ((this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) || (this.selectedOffre?.raisonSocialeTransporteur?.length && !this.selectedOffre?.transporteurCommande)) {
      this.alertService.error(`Veuillez compléter les éléments obligatoires suivants: 
      <ul class="cstm-ul">
      ${(this.selectedOffre?.listDelaiPaiements?.length && !this.selectedOffre?.delaiPaiement) ? "<li>Sélectionner un <b>Mode de paiement</b></li>" : ''}
      ${(this.selectedOffre?.raisonSocialeTransporteur?.length && !this.selectedOffre?.transporteurCommande) ? "<li>Sélectionner un <b>Transporteur</b></li>" : ''}
      </ul>
      `, 'MODAL');
    } else if (!principal?.societe?.numIce) {
      this.alertService.error(`Vous n'avez pas encore spécifié votre numéro ICE, veuillez compléter votre profil.`, 'MODAL');
    } else {
      if (this.selectedOffre.cmdsUnitairesModifie?.length) {
        this.commandeService
          .saveDetailCommandeUnitaire(
            this.selectedOffre.cmdsUnitairesModifie,
            this.managedGroupe?.id
          )
          .subscribe((res) => {
            this.selectedOffre.cmdsUnitairesModifie = [];
            this.goValiderCommandeConsolidee();
          });
      } else {
        this.goValiderCommandeConsolidee();
      }
    }
  }

  goValiderCommandeConsolidee() {
    this.commandeService
      .validerCommandeConsolidee(this.selectedOffre)
      .subscribe((res) => {
        this.envoyerCommandeGeneric();
      });
  }

  modePaiementValueChange(selected: DomainEnumeration): void {
    this.commandeService
      .attacherModePaiement(this.selectedOffre?.enteteCommandeId, selected?.id)
      .subscribe((res) => {
        this.fetchCommandeById(this.commandeId);
      });
  }

  annulerCommande() {
    this.userInputService
      .confirmAlt(
        'Confirmation',
        `Êtes vous sûr de vouloir annuler la commande avec <b>code commande: ${this.commande?.codeCommande}</b> ?`
      )
      .then(
        () => {
          iif(
            () => this.commandeType === TYPE_CMD.GROUPE,
            this.commandeService.annulerCommandeConsolidee((this.commande as Offre)?.enteteCommandeId),
            this.commandeService.annulerCommandeUnitaire((this.commande as Offre)?.enteteCommandeId, this.managedGroupe?.id)
          ).subscribe((_) => {
            this.back();
            this.alertService.successAlt(`La commande a été annulée avec succès.`, 'Commande Annulée', 'MODAL');
          });
        },
        () => null
      );
  }

  cloturerCommande() {
    this.userInputService
      .confirmAlt('Confirmation', `Êtes vous sûr de voulor clôturer cette commande groupe ?`)
      .then(
        () => {
          this.commandeService
            .clotureCommandeConsolidee({ cmdConsolideeId: this.selectedOffre.enteteCommandeId })
            .subscribe((res) => {
              this.back(true);
            });
        },
        () => null
      );
  }

  rendreFinSaisie() {
    this.commandeService.getCommandeUnitaireByCmdConsolideeId(this.commande?.enteteCommandeId).subscribe(res => {
      this.cmdsUnitairesBrouillon = res.filter(
        (cmd) => cmd.etatCommandeAchatGroupe === 'BROUILLON'
      );

      setTimeout(() => {
        this.listenToCmdBrouillonVoirPlusClick();
      }, 500);

      this.userInputService
        .confirmAlt(
          'Confirmation',
          !this.cmdsUnitairesBrouillon.length
            ? `Êtes vous sûr de vouloir rendre fin à la saisie de cette commande ?`
            : `Les pharmaciens suivants ont des commandes unitaires en êtat brouillon, voulez-vous vraiment finir la saisie ?
            <ul class="my-1">
              <li><b>Dr. ${this.cmdsUnitairesBrouillon[0]?.client?.nomResponsable} | ${this.cmdsUnitairesBrouillon[0]?.client?.raisonSociale}</b></li>
              ${this.cmdsUnitairesBrouillon?.length > 1 ? `<li><b>Dr. ${this.cmdsUnitairesBrouillon[1]?.client?.nomResponsable} | ${this.cmdsUnitairesBrouillon[1]?.client?.raisonSociale}</b></li>` : ''}
              ${this.cmdsUnitairesBrouillon?.length > 2 ? `<li><u class="pointer-cus cmd-brouillon-voir-plus">Voir plus <i class="bi bi-chevron-double-right"></i></u></li>` : ''}
            </ul>
          `
        )
        .then(
          () => {
            this.fsOffreService
              .finSaisieFsOffre(this.commande?.enteteCommandeId)
              .subscribe((res) => {
                this.fetchCommandeById(this.commandeId || this.selectedOffre?.enteteCommandeId);
                this.alertService.successAlt(`La saisie de cette commande a été arrêtée avec succès.`, 'Fin de Saisie', 'MODAL');
              });
          },
          () => null
        );
    });
  }

  listenToCmdBrouillonVoirPlusClick() {
    const element = document.querySelector('.cmd-brouillon-voir-plus') as HTMLElement;

    if (element) {
      this.cmdBrouillonClickListener = this.renderer.listen(element, 'click', (event) => {
        event.preventDefault();

        this.modalService.open(this.cmdsBrouillonModal, { size: 'lg', centered: true });
      });
    }
  }

  reactiverLaSaisie() {
    if (this.selectedMemberIds?.length) {
      this.userInputService
        .confirmAlt(
          'Confirmation',
          `Êtes vous sûr de vouloir réactiver la saisie pour les membres sélectionnés ?`
        )
        .then(
          () => {
            this.commandeService.getCommandeUnitaireByCmdConsolideeId(this.commande?.enteteCommandeId)
              .subscribe((res) => {
                const cmdUnitaires = res?.filter(
                  (cmd) =>
                    this.selectedMemberIds?.includes(cmd?.enteteCommandeId) &&
                    cmd?.etatCommandeAchatGroupe === 'TRANSMIS'
                );

                const cmdUnitairesIds = cmdUnitaires.map(
                  (cmd) => cmd?.enteteCommandeId
                );

                if (cmdUnitairesIds?.length) {
                  this.commandeService
                    .reactiverCommandeUnitaire(cmdUnitairesIds, this.managedGroupe?.id)
                    .subscribe((res) => {
                      this.modalService.dismissAll();
                      this.fetchCommandeById(this.commandeId || this.selectedOffre.enteteCommandeId);
                      this.alertService.successAlt(
                        `La saisie a été réactivée pour les membres sélectionnés.`,
                        'Réactivation Saisie',
                        'MODAL'
                      );
                    });
                } else {
                  this.reactiverOffre();
                }
              });
          },
          () => null
        );
    }
  }

  reactiverOffre() {
    this.fsOffreService
      .reactiverFsOffre(this.commande?.enteteCommandeId)
      .subscribe((res) => {
        this.modalService.dismissAll();
        this.fetchCommandeById(this.commandeId || this.selectedOffre.enteteCommandeId);

        this.alertService.successAlt(`La saisie a été réactivée pour les membres sélectionnés.`, 'Réactivation Saisie', 'MODAL');
      });
  }

  paste(event: any) {
    event.preventDefault();
  }

  back(modified = true) {
    if (!this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR', 'ROLE_SUPER_ADMIN'])) {
      this.cmdGroupeCreated && (modified = true);
      this.submitted = modified;

      let pageMode = this.commandeType === TYPE_CMD.GROUPE ? 'groupe' : 'liste';

      if (this.commandeType === TYPE_CMD.INDIVIDUELLE) {
        pageMode += '/individuelle';
      }

      this.route.queryParams.subscribe((params) => {
        if (params['from'] === 'bl-unitaire' && params['ref']) {
          this.router.navigateByUrl(
            `/achats-groupes/bons-livraison/edit/${params['ref']}/unitaire`
          );
        } else if (params['from'] === 'bl' && params['ref']) {
          this.router.navigateByUrl(
            `/achats-groupes/bons-livraison/edit/${params['ref']}?readOnly=true`
          );
        } else if (params['from'] && params['from'] === 'cmds-admin') {
          this.router.navigateByUrl(`achats-groupes/commandes/liste/commandes/admin`);
        } else {
          if (modified || this.cmdGroupeChangesApplied) {
            this.router.navigateByUrl(`achats-groupes/commandes/${pageMode}`, {
              state: { modified: true },
            });
          } else {
            this.pageMode
              ? this.router.navigate(['achats-groupes/offres/liste'], {
                queryParams: { mode: this.pageMode },
              })
              : this.router.navigateByUrl(`achats-groupes/commandes/${pageMode}`);
          }
        }
      });
    } else if (this.authService.hasAnyAuthority(['ROLE_AGENT_FOURNISSEUR'])) {
      this.router.navigateByUrl('achats-groupes/commandes/liste/laboratoire');
    } else if (this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN'])) {
      const qParams = this.route.snapshot.queryParams;

      if (qParams['from'] && qParams['from'] === 'cmds-admin') {
        this.router.navigateByUrl(`achats-groupes/commandes/liste/commandes/admin`);
      } else {
        this.router.navigateByUrl(`achats-groupes/commandes/liste/${this.commandeType === TYPE_CMD.GROUPE ? 'groupe' : 'individuelle'}/admin`);
      }
    }
  }

  validerLesChangements(): void {
    if (this.selectedOffre.cmdsUnitairesModifie?.length) {
      this.commandeService
        .saveDetailCommandeUnitaire(
          this.selectedOffre.cmdsUnitairesModifie,
          this.managedGroupe?.id
        )
        .subscribe((res) => {
          this.fetchCommandeById(this.commandeId || this.selectedOffre.enteteCommandeId);

          this.selectedOffre.cmdsUnitairesModifie = [];
          this.alertService.successAlt(`Vos changements ont été validés avec succès.`, 'Validation des Changements', 'MODAL');

          this.cmdGroupeChangesApplied = true;
        });
    } else {
      this.alertService.error("Vous n'avez aucun changement à valider.", 'MODAL');
    }
  }

  openModal(modalContent: any, size: string) {
    this.modalService
      .open(modalContent, {
        ariaLabelledBy: 'modal-basic-title',
        size,
        backdrop: 'static',
        centered: true,
        windowClass: 'fs-cstm-modal',
      })
      .result.then(
        (result) => {
          console.log(result);
        },
        (reason) => {
          console.log('Err!', reason);
        }
      );
  }

  saisirBl(): void {
    if (this.commandeType === TYPE_CMD.INDIVIDUELLE) {
      this.router.navigate([
        '/achats-groupes/bons-livraison/individuelle',
        this.selectedOffre?.enteteCommandeId,
        'edit',
      ]);
      return;
    }
    this.router.navigate([
      'achats-groupes/bons-livraison',
      this.selectedOffre?.enteteCommandeId,
      'edit',
    ]);
  }

  saisirCommandeUnitaire() {
    this.fedSyndicatService.getMyGroupe().then((myGroupe) => {
      this.router.navigateByUrl('achats-groupes/commandes/liste').then(() => {
        this.router.navigate(['achats-groupes/commandes/edit/cmd-unitaire'], {
          queryParams: {
            readOnly: false,
            groupeId: myGroupe?.id,
            offreId: this.selectedOffre?.id,
            cmdConsolideeId: this.selectedOffre?.enteteCommandeId
          },
          replaceUrl: true,
        });
      });
    });
  }

  imprimerCommande(): void {
    if (this.selectedOffre.enteteCommandeId) {
      this.blobUrl = null;

      this.commandeService
        .imprimerCommandeConsolidee(this.selectedOffre.enteteCommandeId)
        .subscribe((res) => {
          this.blobUrl = res;
        });
    }
  }

  imprimerCommandeIndividuelle(): void {
    if (this.commande?.enteteCommandeId) {
      this.blobUrl = null;
      this.offresService.imprimerCommandeIndividuelle(this.commande?.enteteCommandeId).subscribe(res => {
        this.blobUrl = res;
      });
    }
  }

  imprimerCommandeUnitaire(): void {
    if (this.commande?.enteteCommandeId) {
      this.blobUrl = null;
      this.offresService.imprimerCommandeUnitaire(this.commande?.enteteCommandeId).subscribe(res => {
        this.blobUrl = res;
      });
    }
  }

  pushMobileMenuOptions(): void {
    this.deferredActionBtnService.pushPageOptions([
      // ? Actions CMD_UNITAIRE START
      {
        iconClass: 'bi bi-send-fill',
        label: 'Envoyer',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && !this.readOnly && (this.commande?.etatCommandeAchatGroupe === 'ACCEPTEE' || this.commande?.etatCommandeAchatGroupe === 'BROUILLON'),
        action: () => this.enregistrerEtEnvoyerCommande(),
      },
      {
        iconClass: 'mdi mdi-content-save',
        label: 'Enregistrer',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && !this.readOnly,
        action: () => this.enregistrerCommande(),
      },
      {
        iconClass: 'mdi mdi-refresh',
        label: 'Réinitialiser',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && !this.readOnly,
        action: () => (this.offreForm.reset({ resetOption: 'O' }), this.openModal(this.resetModal, 'md')),
      },
      {
        iconClass: 'bi bi-printer-fill',
        label: 'Imprimer',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && this.readOnly && !this.cmdConsolideeId,
        action: () => this.imprimerCommandeUnitaire(),
      },
      {
        iconClass: 'bi bi-backspace-fill',
        label: 'Annuler',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && this.readOnly && this.commande?.etatCommandeAchatGroupe === 'BROUILLON',
        action: () => this.annulerCommande(),
      },
      {
        iconClass: 'bi bi-send-fill',
        label: 'Envoyer',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && this.readOnly && this.commande?.etatCommandeAchatGroupe !== 'ENVOYEE' &&
          this.commande?.etatCommandeAchatGroupe !== 'TRANSMIS' && this.commande?.etatCommandeAchatGroupe !== 'ANNULEE'
          && this.commande?.etatCommandeAchatGroupe !== 'EN_LIVRAISON' && this.commande?.etatCommandeAchatGroupe !== 'LIVREE',
        action: () => this.envoyerCommandeUnitaire(),
      },
      {
        iconClass: 'bi bi-receipt',
        label: 'Consulter BLS',
        shouldShow: this.commandeType === TYPE_CMD.UNITAIRE && this.readOnly && this.canConsultBls && (this.commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || this.commande?.etatCommandeAchatGroupe === 'LIVREE'),
        action: () => this.consulterBLS(),
      },
      // ? Actions CMD_UNITAIRE END

      // ? Actions CMD_INDIVIDUELLE START
      {
        iconClass: 'bi bi-send-fill',
        label: 'Envoyer',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && !this.readOnly,
        action: () => this.enregistrerEtEnvoyerCommandeIndividuelle(),
      },
      {
        iconClass: 'mdi mdi-content-save',
        label: 'Enregistrer',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && !this.readOnly,
        action: () => this.enregistrerCommandeIndividuelle(),
      },
      {
        iconClass: 'mdi mdi-refresh',
        label: 'Réinitialiser',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && !this.readOnly,
        action: () => (this.offreForm.reset({resetOption: 'O'}), this.openModal(this.resetModal, 'md')),
      },
      {
        iconClass: 'bi bi-trash',
        label: 'Supprimer',
        targetRoles: ['ROLE_AGENT_POINT_VENTE', 'ROLE_RESPONSABLE', 'ROLE_NATIONAL'],
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && this.readOnly && this.commande?.commandStatut === 'BROUILLON',
        action: () => this.supprimerCommandeIndividuelle(),
      },
      {
        iconClass: 'bi bi-send-fill',
        label: 'Envoyer',
        targetRoles: ['ROLE_AGENT_POINT_VENTE', 'ROLE_RESPONSABLE', 'ROLE_NATIONAL'],
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && this.readOnly && this.commande?.commandStatut === 'BROUILLON',
        action: () => this.envoyerCommandeGeneric(this.shouldSkipRequiredFieldsCheck()),
      },
      {
        iconClass: 'bi bi-plus-circle-fill',
        label: 'Saisir BL',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && this.readOnly && (this.commande?.commandStatut === 'ENVOYEE' || this.commande?.commandStatut === 'EN_LIVRAISON'),
        action: () => this.saisirBl(),
      },
      {
        iconClass: 'bi bi-receipt',
        label: 'Consulter BLS',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && this.readOnly && (this.commande?.commandStatut === 'EN_LIVRAISON' || this.commande?.commandStatut === 'LIVREE'),
        action: () => this.consulterBLS(),
      },
      {
        iconClass: 'bi bi-printer-fill',
        label: 'Imprimer',
        shouldShow: this.commandeType === TYPE_CMD.INDIVIDUELLE && this.readOnly && (this.commande?.commandStatut === 'ENVOYEE' || this.commande?.commandStatut === 'EN_LIVRAISON' || this.commande?.commandStatut === 'LIVREE'),
        action: () => this.imprimerCommandeIndividuelle(),
      },
      // ? Actions CMD_INDIVIDUELLE END

      // ? Actions CMD_GROUPE START
      {
        iconClass: 'bi bi-printer-fill',
        label: 'Imprimer',
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.canPrintCmdConsolidee(),
        action: () => this.imprimerCommande(),
      },
      {
        iconClass: 'bi bi-backspace-fill',
        label: 'Annuler',
        targetRoles: ['ROLE_RESPONSABLE'],
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && (this.commande?.etatCommandeAchatGroupe === 'ACCEPTEE' || this.commande?.etatCommandeAchatGroupe === 'FIN_SAISIE'),
        action: () => this.annulerCommande(),
      },
      {
        iconClass: 'bi bi-shield-lock-fill',
        label: 'Fin de saisie',
        targetRoles: ['ROLE_RESPONSABLE'],
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.commande?.etatCommandeAchatGroupe === 'ACCEPTEE',
        action: () => this.rendreFinSaisie(),
      },
      {
        iconClass: 'bi bi-unlock-fill',
        label: 'Réactiver la saisie',
        targetRoles: ['ROLE_RESPONSABLE'],
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.commande?.etatCommandeAchatGroupe === 'FIN_SAISIE',
        action: () => this.cmdsUnitaires?.length ? this.openModal(this.reactivationSaisieModal, 'xl'): this.reactiverOffre(),
      },
      {
        iconClass: 'bi bi-plus-circle-fill',
        label: 'Saisir Commande',
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.commande?.etatCommandeAchatGroupe === 'ACCEPTEE' && !this.commandeUnitaireExists,
        action: () => this.saisirCommandeUnitaire(),
      },
      {
        iconClass: 'bi bi-backspace-fill',
        label: 'Clotûrer',
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.isSupporteur && (this.commande?.etatCommandeAchatGroupe === 'ENVOYEE' || this.commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || this.commande?.etatCommandeAchatGroupe === 'LIVREE') && this.commande?.etatCommandeAchatGroupe !== 'LIVREE',
        action: () => this.cloturerCommande(),
      },
      {
        iconClass: 'bi bi-plus-circle-fill',
        label: 'Saisir BL',
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.isSupporteur && (this.commande?.etatCommandeAchatGroupe === 'ENVOYEE' || this.commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || this.commande?.etatCommandeAchatGroupe === 'LIVREE') && this.commande?.etatCommandeAchatGroupe !== 'LIVREE',
        action: () => this.saisirBl(),
      },
      {
        iconClass: 'bi bi-receipt',
        label: 'Consulter BLS',
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.isSupporteur && (this.commande?.etatCommandeAchatGroupe === 'EN_LIVRAISON' || this.commande?.etatCommandeAchatGroupe === 'LIVREE') && this.readOnly,
        action: () => this.consulterBLS(),
      },
      {
        iconClass: 'bi bi-backspace-fill',
        label: 'Réfuser',
        targetRoles: ['ROLE_RESPONSABLE'],
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && (this.selectedOffre?.etatCommandeAchatGroupe === 'EN_ATTENTE' || this.selectedOffre?.etatCommandeAchatGroupe !== 'REFUSEE'),
        action: () => this.refuserOffre(),
      },
      {
        iconClass: 'bi bi-unlock-fill',
        label: 'Activer la saisie',
        targetRoles: ['ROLE_RESPONSABLE'],
        shouldShow: this.commandeType === TYPE_CMD.GROUPE && this.selectedOffre?.etatCommandeAchatGroupe === 'EN_ATTENTE',
        action: () => this.activerLaSaisie(),
      },
      // ? Actions CMD_GROUPE END
      {
        label: 'Rechercher produit',
        iconClass: 'mdi mdi-magnify',
        shouldShow: true,
        action: () => this.rechercherProduitRef?.openSearchModal(),
      },
      {
        iconClass: 'mdi mdi-close',
        label: 'Quitter',
        shouldShow: true,
        action: () => this.back(false),
      },
    ]);
  }



  hasUnsaved(): boolean {
    const comparatorObj = cloneDeep(this.selectedOffre);

    if (comparatorObj && (this.commandeType === TYPE_CMD.UNITAIRE || this.commandeType === TYPE_CMD.INDIVIDUELLE)) {
      if (
        comparatorObj?.listeBlocs &&
        comparatorObj?.mapBlocsById &&
        this.initialSelectedOffreObj?.listeBlocs &&
        this.initialSelectedOffreObj?.mapBlocsById
      ) {
        delete comparatorObj['listeBlocs'];
        delete comparatorObj['mapBlocsById'];

        delete this.initialSelectedOffreObj['listeBlocs'];
        delete this.initialSelectedOffreObj['mapBlocsById'];
      }
    }

    return (!this.readOnly && !isEqual(this.initialSelectedOffreObj, comparatorObj ?? {}));
  }


  hasUnsavedData(): boolean {
    return (!this.submitted && this.hasUnsaved());
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();

    if (this.cmdBrouillonClickListener) {
      this.cmdBrouillonClickListener();
    }

    this.deferredActionBtnService.pushPageOptions([]);
  }
}
