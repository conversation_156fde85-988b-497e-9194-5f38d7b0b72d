

.card, .card-header, .b-radius {
    border-radius: var(--winoffre-base-border-radius);
}

kendo-grid {
  flex: 1;
  overflow: auto;
}


::ng-deep #FEDERATION_SYNDICAT-container .bg-card-header {
    top: 0;
    left: 0;
    width: auto;
    position: relative;
    background: var(--fs-grid-primary);
    padding:5px;

    border-radius: 10px 0px 10px 0px;

    input::placeholder {
        color: rgb(255, 255, 255, 0.75);
        font-weight: 700;
    }

    input::selection {
        background: var(--fs-primary-tint);
    }

    input,
    input:focus,
    input:active {
        padding: 0;
        color: #fff !important;
        font-weight: 700;
        font-size: clamp(1.3rem, 2vw, 1.8rem);
        border: transparent !important;
        padding-left: 10px;
        outline: none;
        width: 100%;
        background:transparent;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

::ng-deep #WIN_GROUPE-container .bg-card-header {
    top: 0;
    left: 0;
    width: auto;
    position: relative;
    background: var(--fs-group-grid);
    padding:5px;

    border-radius: 10px 0px 10px 0px;

    input::placeholder {
        color: rgb(255, 255, 255, 0.75);
        font-weight: 700;
    }

    input::selection {
        background: var(--fs-primary-tint);
    }

    input,
    input:focus,
    input:active {
        padding: 0;
        color: #fff !important;
        font-weight: 700;
        font-size: clamp(1.3rem, 2vw, 1.8rem);
        border: transparent !important;
        padding-left: 10px;
        outline: none;
        width: 100%;
        background:transparent;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

::ng-deep #FEDERATION_SYNDICAT-container {
  .fs-grid .k-grid-toolbar{
    background-color: var(--fs-grid-primary);
    border-bottom: none;
  }
}

::ng-deep #WIN_GROUPE-container {
  .fs-grid .k-grid-toolbar{
    background-color: var(--fs-group-grid);
    border-bottom: none;
  }
}

label,
.form-control {
    font-size: 1rem;
    font-weight: 700;
    color: black;
}

.input-group {
    .form-control {
        border-radius: var(--winoffre-base-border-radius);
    }

    button {
      color: black;
      font-size: 1rem;
      font-weight: 700;
      border-radius: 10px;
      border: 1px solid #f3f3f3;
    }
}

.table-container {
  max-height: 300px;
  overflow-y: auto;

  scrollbar-width: thin !important;
  scrollbar-color: var(--fs-secondary-light) white !important;
  border-radius: var(--winoffre-base-border-radius)
}

.table-row {
    display: flex;
    margin-bottom: 5px;
    cursor: pointer;
  }

  .table-cell {
    flex: 1;
    padding: 2px;

    color: black;
    font-weight: 600;
    width: fit-content;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  ::ng-deep .fs-cstm-modal .modal-content{
    border-radius: 10px;
  }

 ::ng-deep .status-switch-align .k-switch-label-on{
    margin-top: 2px;
    margin-left: 3px;
  }

 ::ng-deep .status-switch-align .k-switch-label-off{
    margin-top: 2px;
    margin-right: 3px;
  }

 ::ng-deep .k-grid .k-grid-header .k-header{
    border: none;
    background-color: var(--fs-group-grid);
    color: #fff;
  }
  .k-grid .k-grid-header .k-header a{
    color: white;
  }
 ::ng-deep .k-grid tr {
    cursor: pointer;
  }

  ::ng-deep.k-grid-header {
    border: none !important;
  }
  ::ng-deep.k-header th {
    border: none !important;
  }
  .actions-icons {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-inline: 10px;
    padding-block: 8px;
    border-radius: 11px;


    i {
      font-size: 18px;
      line-height: 1;
      cursor: pointer;
    }
  }


  .action-danger{
    background-color: var(--fs-danger);
    border-color: var(--fs-danger);
    &:hover{

      opacity: 0.9;
    }
    &:active {
      opacity: 0.7;
    }
    i{
      color: white;
    }
  }
  .action-success{
    background-color: var(--fs-success);
    border-color: var(--fs-success);
    &:hover{

      opacity: 0.9;
    }
    &:active {
      opacity: 0.7;
    }
    i{
      color: white;
    }
  }

.table-row{
  padding: 0.375rem 1.5rem;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

::ng-deep #client-picker-input .dropdown-item{
  padding: 0 !important;
  width: 100%;
}


::ng-deep #WIN_GROUPE-container .action-back {
  background: var(--wf-primary-400);
}

::ng-deep #FEDERATION_SYNDICAT-container .action-back {
  background: var(--fs-primary-500);
}

.modal-custom-size {
  width: 500px;
  max-width: 90%;
}


.responsive-actions{


  @media screen and (max-width: 768px) {
    display: flex;
    justify-content: space-between !important;
    align-items: center;
    padding-inline: 10px;
    padding-block: 8px;
    background: white;
    border-top: #ccc 1px solid;

    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    margin: 0;

  }
}

.btn-success{
  background-color: var(--fs-success);
  border-color: var(--fs-success);
  &:hover{
    opacity: 0.8 !important;
  }
  i{
    color: white;
  }
  &:disabled{
    pointer-events: none  !important;
  }
}


.badge-chef-groupe{
  background-color: #8661c7;
  color: white;
  padding-block: 0.25rem;
  padding-inline: 0.5rem;
  border-radius: 10px;
  min-width: 100px;
  display: block;
  text-align: center;
}
.badge-member-groupe{
  background-color: #a78fc5;
  color: white;
  padding-block: 0.25rem;
  padding-inline: 0.5rem;
  border-radius: 10px;
  min-width: 100px;
  display: block;
  text-align: center;
}

@media screen and (max-width: 768px) {
  .main-card {
    margin-bottom: 60px !important;
  }
}
