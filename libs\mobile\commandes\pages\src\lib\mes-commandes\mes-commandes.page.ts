import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { Commande, Fournisseur, OffresService, Pagination } from '@wph/data-access';
import {
  InfiniteScrollCustomEvent,
  ModalController,
  NavController,
  Platform,
} from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { Subject, Subscription, debounceTime, distinctUntilChanged, map, takeUntil, tap } from 'rxjs';
import * as moment from 'moment';
import { AuthService } from '@wph/core/auth';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { StateModalComponent } from '@wph/mobile/shared';

@Component({
  selector: 'wph-mes-commandes',
  templateUrl: './mes-commandes.page.html',
  styleUrls: ['./mes-commandes.page.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class MesCommandesPage implements OnInit, OnD<PERSON>roy {
  data: Commande[] = [];
  searchValue: string;
  page = 1;
  totalPages = 0;
  firstLoad = true;
  statut: string[] = ['B'];
  error = false;
  societe: Fournisseur;
  startsWith = new RegExp('^(cd|cmd)-?');

  searchQuery$: Subject<string> = new Subject();
  unsubscribe$: Subject<boolean> = new Subject();
  backBtnSubscription: Subscription | null = null;

  pagination: Pagination = {
    skip: 0,
    pageSize: 15,
    sortField: null,
    sortMethod: null,
  };
  loading = true;

  /* Commande filter options */
  filterOptions = [
    { text: 'Commandée', val: 'N', isChecked: true, indicator: 'success' },
    { text: 'Refusée', val: 'R', isChecked: true, indicator: 'danger' },
    { text: 'Acceptée', val: 'AC', isChecked: true, indicator: 'info' },
    { text: 'Supprimée', val: 'S', isChecked: true, indicator: 'danger' },
    { text: 'Annulée', val: 'A', isChecked: true, indicator: 'warning' }
  ];

  selectedFiltersOptions: string[];

  isFilterApplied: boolean = false;
  isFilterModalOpen: boolean = false;
  commandeFilterForm: FormGroup | null = null;

  get filterControls() {
    return this.commandeFilterForm.controls;
  }

  constructor(
    private ngZone: NgZone,
    private fb: FormBuilder,
    private platform: Platform,
    private modalController: ModalController,
    private offresService: OffresService,
    private navController: NavController,
    private route: ActivatedRoute,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.route.data.subscribe((v) => {
      switch (v['statut']) {
        case 'B':
          this.statut = ['B'];
          this.handleRefresh();
          break;
        case 'V':
          this.statut = ['R', 'AC', 'N', 'A', 'S'];
          this.handleRefresh();
          break;
      }
    });

    this.commandeFilterForm = this.fb.group({
      statusOptions: [this.fb.array(this.filterOptions.map(v => {
        return this.fb.group({
          text: [v.text],
          val: [v.val],
          isChecked: [v.isChecked],
          indicator: [v.indicator]
        })
      }))],
      segment: [null],
      dateDebut: [new Date().toISOString()],
      dateFin: [new Date().toISOString()]
    });

    this.searchQuery$
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(500),
        distinctUntilChanged(),
        map((value: string) => value.toLowerCase())
      )
      .subscribe(value => {
        this.searchValue = value;

        this.resetData();
        this.getData();
      });

    this.societe = this.authService.getPrincipal()?.societe;

    this.backBtnSubscription = this.platform.backButton.subscribe(() => {
      if (this.statut.includes('B')) {
        this.ngZone.run(() => {
          this.navController.navigateBack('/commandes');
        });
      }
    })
  }

  getData(ev?: Event) {
    this.error = false;

    let dateRange = {};
    const filterFormValue = this.commandeFilterForm?.getRawValue();

    if (this.isFilterApplied && (this.commandeFilterForm?.get('dateDebut')?.dirty || this.commandeFilterForm?.get('dateFin')?.dirty)) {
      const dateDebut: Date = new Date(filterFormValue?.dateDebut);
      dateDebut && dateDebut?.setHours(0, 0, 0);

      this.filterControls['dateDebut']?.dirty && (dateRange['dateDebutCommande'] = moment(dateDebut).format('YYYY-MM-DD HH:mm:ss'));
      this.filterControls['dateFin']?.dirty && (dateRange['dateFinCommande'] = moment(filterFormValue?.dateFin).format('YYYY-MM-DD HH:mm:ss'));
    }

    this.offresService
      .searchCommandes(
        {
          [
            this.startsWith.test(this.searchValue) ?
              'codeCommande' :
              'titre'
          ]: !this.isFilterApplied ? this.searchValue : undefined,
          statut: this.selectedFiltersOptions || this.statut,
          nonExpireesUniquement: 'O',
          ...dateRange
        },
        this.pagination
      )
      .pipe(
        tap({
          error: (_err) => {
            this.error = true;
            this.loading = false;
            this.firstLoad = false;

            if (ev) {
              (ev as InfiniteScrollCustomEvent).target.complete();
            }
          },
        })
      )
      .subscribe((data) => {
        this.totalPages = data.totalPages;
        if (ev) {
          this.data.push(...data.content);

          this.pagination.skip += this.pagination.pageSize;
          this.page++;

          (ev as InfiniteScrollCustomEvent).target.complete();
        } else {
          this.page = 1;
          this.data = [...data.content];
          this.pagination.skip += this.pagination.pageSize;
        }
        this.error = false;
        this.firstLoad = false;
        this.loading = false;
      });
  }

  handleRefresh(event?: Event) {
    this.resetData();
    this.getData(event);
  }

  get statutTranslation() {
    if (this.statut.includes('A')) {
      return 'Commandes';
    } else if (this.statut.includes('B')) {
      return 'Brouillons';
    } else if (this.statut.includes('V')) {
      return 'Commandes';
    } else {
      return '';
    }
  }

  onItemClickFunc(offre: any, event: Event) {
    if (event) {
      event.stopPropagation();
    }

    this.navController.navigateRoot(
      '/commandes/' + offre.id + '?offre=true',
      { replaceUrl: true }
    );
  }

  onTextChangeFunc($event: any) {
    this.searchQuery$.next($event.target.value);
  }

  resetData() {
    this.loading = true;
    this.page = 1;
    this.pagination = {
      skip: 0,
      pageSize: 15,
      sortField: null,
      sortMethod: null,
    };
    this.totalPages = 0;
    this.data = [];
  }

  doInfinite(infiniteScroll) {
    this.getData(infiniteScroll);
  }

  async handlepress(commande: Commande) {
    if (moment(commande?.offre?.dateFin).diff(new Date(), 'days') < 0) {
      this.presentErrorMessageModale();
    } else {
      if (commande.statut === 'B') {
        this.navController.navigateRoot('commandes/' + commande.id);
      } else {
        this.navController.navigateForward('commandes/' + commande.id, {
          queryParams: { readOnly: true }
        });
      }
    }
  }

  async presentErrorMessageModale() {
    console.log('presenting');
    const alert = await this.modalController.create({
      cssClass: 'small-modal',
      component: StateModalComponent,
      componentProps: {
        message: "l'offre est expirée",
        type: 'error',
      },
    });

    await alert.present();
  }

  clearFilters() {
    this.searchValue = '';

    this.isFilterApplied = false;
    this.handleRefresh();
  }

  applyOrClearFilters(isFilterApplied: boolean) {
    const commandStatusOptions = (this.filterControls['statusOptions']?.value as FormArray).value;

    this.selectedFiltersOptions = !this.statut.includes('B') ? commandStatusOptions
      .filter((opt) => opt.isChecked)
      .map((opt) => opt.val) : null;

    if (isFilterApplied) {
      this.isFilterApplied = true;
      this.handleRefresh();
    } else {
      this.clearFilters();
    }

    this.isFilterModalOpen = false;
  }

  trackItems(_index: number, item: Commande) {
    return item?.codeCommande;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
    this.backBtnSubscription.unsubscribe();
  }
}
