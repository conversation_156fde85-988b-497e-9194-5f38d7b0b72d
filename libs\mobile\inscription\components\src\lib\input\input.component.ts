import {Component, forwardRef, Input, OnInit} from '@angular/core';
import {ControlContainer, ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR} from "@angular/forms";

@Component({
  selector: 'wph-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.scss'],
  providers: [
  {
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => InputComponent),
    multi: true
  }
]
})
export class InputComponent implements ControlValueAccessor, OnInit {
  @Input() formControlName: string;
  @Input() title: string;
  @Input() type = 'text';
  @Input() required = false;
  @Input() isPassword = false;
  @Input() readonly: boolean = false;
  @Input() placeholder: string = '';

  value: any;
  onChange: any = () => {};
  onTouch: any = () => {};
  parentForm: FormGroup;
  control: FormControl;

  constructor(private controlContainer: ControlContainer) {}

  ngOnInit() {
    this.parentForm = this.controlContainer.control as FormGroup;
    this.control = this.parentForm.controls[this.formControlName] as FormControl;
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  updateValue(value: any) {
    console.log('val change')
    this.value = value;
    this.onChange(value);
    this.onTouch();
  }

}
