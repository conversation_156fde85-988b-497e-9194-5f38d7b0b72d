// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  demo: "saas", // other possible options are creative and modern
  GOOGLE_MAPS_API_KEY: "AIzaSyDpgQMpcfx1QU-8SM-ljcgjG4xrYtIYby4",
  max_menu_notifs: 7,
  // base_url: "https://vps5.sophatel.com:4201", // PharmaHub Dev
  // base_url: "http://***************:5001", // LocalHost
  // base_url: "https://pharmahub.ma", // PharmaHub Prod
  base_url: "https://vps5.sophatel.com:4206", // Achats Groupés Recette
  // base_url: "https://vps5.sophatel.com:4202", // Achats Groupés Dev
  // base_url: "https://lacentralepharma.ma", // Achats Groupés Prod
  // base_url: "https://groupe.winplus.ma", //  Win Groupe Prod
  fs_base_url: "http://vps5.sophatel.com:5001",
  cdn_base_url: "https://cdn.sophatel.com",
  multi_user: false,
  enable_push_notification: false,
  ATTACH_TAG_MANAGER: true,
  GTAG_ID: 'GTM-KDTH5JMZ',
  TAG_MANAGER_CONTAINER_ID: 'G-7NGL5G02FD',
  base64Key:'c2VAcmV0S2V5eTEyMzQ1IQ==',
  platform: 'WEB',
  // ACHATS_GROUPES_HOST: ['localhost'],
  WIN_GROUPE_HOST: ['localhost'],
  PHARMA_HUB_HOST: ['***************']
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
