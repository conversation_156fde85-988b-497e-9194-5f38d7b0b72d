import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from "@angular/core";
import { BlocOffre, Commande, Fournisseur, Offre, OffresService } from "@wph/data-access";
import { ActivatedRoute, Router } from "@angular/router";
import SwiperCore, { Swiper } from "swiper";
import { Navigation, Pagination } from 'swiper/modules';
import { ActionSheetController, AlertController, LoadingController, ModalController, NavController, NavParams, Platform } from "@ionic/angular";
import { StateModalComponent } from "../../../../../shared/src/lib/state-modal/state-modal.component";
import { ConfirmationCommandePage } from "../confirmation-commande/confirmation-commande.page";
import { Subscription, iif, map, switchMap, tap } from "rxjs";
import { AuthService } from "@wph/core/auth";
import { AccesClientService, ClientFournisseur } from "@wph/shared";

SwiperCore.use([Pagination, Navigation]);


@Component({
  selector: "wph-commande",
  templateUrl: "./commande.page.html",
  styleUrls: ["./commande.page.scss"],
  providers: [NavParams],
  encapsulation: ViewEncapsulation.None
})
export class CommandePage implements OnInit, OnDestroy {

  swiperConfig: any;

  firstLoad = true;

  swiper: Swiper;


  offres: Offre[];
  selectedOffre: Offre;
  id: string;
  validCommande = true;
  readOnly = false;
  hasAccess: boolean = true;
  offre = false;
  etatBar: string;
  printing: boolean;
  blobUrl: string;
  isSaved: boolean;
  savedCommande: Commande;
  selectedFournisseur: Fournisseur;

  activeIndex = 0;

  currentChanges = [];
  isPresented: boolean = false;

  stateCommanderBtn: boolean = false;

  selectedClient: Fournisseur | null = null;
  selectedClientLocal: ClientFournisseur | null = null;

  clientTypeaheadModalIsOpen: boolean = false;
  clientFournisseur: boolean = false;

  backBtnSubscription: Subscription;

  currentUserSociete: Fournisseur | null = null;

  hasRoleFournisseurOuCommercial: boolean = false;

  constructor(
    private modalController: ModalController,
    private offresService: OffresService,
    private route: ActivatedRoute,
    private router: Router,
    private platform: Platform,
    private navController: NavController,
    private actionSheetController: ActionSheetController,
    private loadingCtrl: LoadingController,
    private alertController: AlertController,
    private authService: AuthService,
    private accesClientService: AccesClientService
  ) { }

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.initPage();
      this.initSwiperConfig();
      this.savedCommande = this.router.getCurrentNavigation().extras.state as any;
      this.isSaved = !!this.savedCommande;
    });

    this.backBtnSubscription = this.platform.backButton.subscribeWithPriority(100, (processNextHandler) => {
      if (!this.isPresented) {
        this.isPresented = true;
        this.readOnly ? this.back() : this.quitter();
      }

      processNextHandler();
    });

    const { societe, authorities } = this.authService.getPrincipal();

    this.currentUserSociete = societe;

    this.hasRoleFournisseurOuCommercial = authorities.includes('ROLE_AGENT_FOURNISSEUR') || authorities.includes('ROLE_AGENT_COMMERCIAL');
  }


  initPage() {
    const qParams = this.route.snapshot.queryParams;
    const params = this.route.snapshot.paramMap;
    // this.offres = offres;
    this.offresService.getValidCommandeObservable().subscribe(() => this.checkifvalid());
    this.offresService.currentEtatBar.subscribe(etat => this.etatBar = etat);
    this.offresService.getListeOffres().subscribe(data => {
      this.offres = data;
      for (const offre of this.offres) {
        this.offresService.initialiserEtatOffre(offre);
        this.offresService.palierOffreTraitement(offre);
      }
    });

    this.offre = (qParams as any).offre;
    this.readOnly = (qParams as any).readOnly;
    this.hasAccess = !(this.readOnly && this.offre);
    this.id = params.get("id");
    const distributeur = (qParams as any)?.selectedFournisseur ? JSON.parse((qParams as any)?.selectedFournisseur) : undefined;

    if (this.id) {
      if (this.offre) {
        this.offresService.getOffreById(+this.id).subscribe((data => {

          this.selectedOffre = data;
          if (distributeur) {
            this.selectedOffre.distributeur = distributeur;
          }
          this.offresService.initialiserEtatOffre(this.selectedOffre);
          this.offresService.palierOffreTraitement(this.selectedOffre);
          this.firstLoad = false;
        }));

      } else {
        this.offresService.getCommandesById(+this.id).subscribe(
          (data) => {
            this.selectedOffre = data;
            if (distributeur) {
              this.selectedOffre.distributeur = distributeur;
            }

            this.offresService.initialiserEtatOffre(this.selectedOffre);
            this.offresService.palierOffreTraitement(this.selectedOffre);

            if (this.currentUserSociete?.id !== this.selectedOffre?.client?.id) {
              this.selectedClient = this.selectedOffre?.client;

              this.getClientLocal();
            } else {
              this.firstLoad = false;
            }

          }
        );
      }


    }
  }

  initSwiperConfig() {
    this.swiperConfig = {
      autoHeight: true,
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
        renderBullet: (index, className) => {
          const cl = this.activeIndex === index ? 'swiper-pagination-bullet-active' : '';
          let classBorder = '';
          if (!this.currentChanges[index] && index !== 0) {
            classBorder = 'non-touched';
          } else if (index === 0 && !this.currentChanges.includes(true)) {
            classBorder = 'non-touched';
          } else if (this.selectedOffre?.listeBlocs[index > 0 ? index - 1 : 0]?.messageEtat) {
            classBorder = 'error-ls';
          } else if (
            index > 0 &&
            !this.selectedOffre?.listeBlocs[index > 0 ? index - 1 : 0]?.messageEtat &&
            this.selectedOffre?.listeBlocs[index > 0 ? index - 1 : 0].totalQteCmd > 0
          ) {
            classBorder = 'matt';
          } else if (index === 0 && this.selectedOffre?.totalQteCmd > 0) {
            classBorder = this.stateCommanderBtn ? 'error-ls' : 'matt';
          }

          if (
            index > 0 &&
            this.selectedOffre?.commandStatut &&
            !this.selectedOffre?.listeBlocs[index > 0 ? index - 1 : 0]?.messageEtat &&
            this.packHasNoneZeroQte(this.selectedOffre?.listeBlocs[index > 0 ? index - 1 : 0])
          ) {
            classBorder = 'matt';
          }

          if (
            index === 0 &&
            this.hasRoleFournisseurOuCommercial &&
            (!this.selectedOffre?.commandStatut || this.selectedOffre?.commandStatut === 'BROUILLON') &&
            this.selectedOffre?.totalQteCmd > 0 &&
            this.validCommande &&
            !this.selectedClient
          ) {
            classBorder = 'matt-warning';
          } else if (
            index === 0 &&
            this.hasRoleFournisseurOuCommercial &&
            (!this.selectedOffre?.commandStatut || this.selectedOffre?.commandStatut === 'BROUILLON') &&
            this.selectedClient &&
            this.selectedOffre?.totalQteCmd > 0 &&
            this.validCommande
          ) {
            classBorder = 'matt';
          }

          return `<h6 id="swiper-bullet-${index}" class="${cl} ${className} ${classBorder}">${index > 0 ? 'P' + index : 'C'}</h6>`;
        },
        // bulletActiveClass: 'swiper-pagination-bullet-active',

      },
      centeredSlides: true
    };
  }

  public checkifvalid() {
    if (this.selectedOffre?.listeBlocs && this.selectedOffre?.listeBlocs?.length && this.selectedOffre?.listeBlocs?.length > 0) {
      for (const iterator of this.selectedOffre.listeBlocs) {
        if (iterator.etat === "I") {
          this.validCommande = false;
          break;
        }
        this.validCommande = true;
      }

    }
  }

  async save() {
    const loading = await this.loadingCtrl.create({
      message: 'Chargement...',
      duration: 3000
    });

    loading.present();
    this.selectedOffre['client'] = this.selectedClient ? this.selectedClient : null;

    this.selectedOffre['codeClientLocal'] =
      (this.selectedClientLocal instanceof Object) ?
        this.selectedClientLocal?.code : this.selectedClientLocal;

    this.offresService.createCommande(this.selectedOffre).subscribe(data => {

      this.isSaved = true;
      this.savedCommande = data;

      loading.dismiss();
      this.presentMessageModal('La commande a été bien sauvegardée.', 'success').then((success) => {
        console.log('it worked', this.savedCommande);
      });

      this.navController.navigateRoot("/commandes/" + data.id, { state: data, replaceUrl: true });
    }, (err) => {
      loading.dismiss();
      this.presentMessageModal(err.error.message ? err.error.message : 'Erreur, Merci de ressayer!', 'error');
    });
  }

  async presentMessageModal(message: string, type: string) {
    const alert = await this.modalController.create({
      cssClass: 'small-modal',
      component: StateModalComponent,
      componentProps: {
        message: message,
        type: type
      }
    });

    await alert.present();
  }

  async quitter() {
    const alert = await this.alertController.create({
      header: 'Quitter',
      message: 'Êtes-vous sûr de vouloir quitter la saisie de cette commande ?',
      cssClass: 'confirm-cmd',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          handler: () => { this.isPresented = false },
        },
        {
          text: 'Confirmer',
          role: 'confirm',
          handler: () => {
            this.selectedOffre?.commandStatut ?
              this.navController.navigateBack('/commandes/br') :
              this.navController.navigateBack('/offres');
          }
        }
      ]
    });

    await alert.present();
  }

  async annuler() {
    const alert = await this.alertController.create({
      header: `${this.selectedOffre?.commandStatut === 'BROUILLON' ? 'Supprimer' : 'Annuler'}`,
      message: `Êtes-vous sûr de vouloir ${this.selectedOffre?.commandStatut === 'BROUILLON' ? 'supprimer' : 'annuler'} cette commande ?`,
      cssClass: 'confirm-cmd',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          handler: () => { },
        },
        {
          text: 'Confirmer',
          role: 'confirm',
          handler: () => {
            iif(
              () => this.selectedOffre?.commandStatut === 'BROUILLON',
              this.offresService.supprimerCommandeById(this.selectedOffre?.enteteCommandeId),
              this.offresService.annulerCommandeById(this.selectedOffre?.enteteCommandeId)
            )
              .subscribe(() => {
                this.readOnly ? this.back() : this.navController.navigateBack('/commandes/br');
              }, (err) => {
                this.presentMessageModal(err.error.message ? err.error.message : 'Erreur, Merci de ressayer!', 'error');
              });
          }
        }
      ]
    });

    await alert.present();
  }

  async showAnnulerAction() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Actions',
      cssClass: 'my-custom-class',
      buttons: [
        {
          text: `${this.selectedOffre?.commandStatut === 'BROUILLON' ? 'Supprimer' : 'Annuler'}`,
          role: 'destructive',
          icon: `${this.selectedOffre?.commandStatut === 'BROUILLON' ? 'trash-bin-outline' : 'remove-circle-outline'}`,
          cssClass: 'cancel-action',
          handler: () => {
            this.annuler();
          },
        }
      ]
    });

    await actionSheet.present();
  }

  back() {
    !this.hasAccess ?
      this.navController.navigateBack('/offres') :
      this.navController.navigateBack('/commandes/tr');
  }

  nextPage() {
    this.swiper?.slideNext();
  }

  prevPage() {
    this.swiper?.slidePrev();
  }

  get items() {
    return [{}, ...this.selectedOffre?.listeBlocs];
  }

  setSwiperInstance(swiper: any) {
    this.swiper = swiper;
  }

  onSlideChange() {
    this.activeIndex = this.swiper.activeIndex;

    const activeSwiperBulletEl = document.getElementById(`swiper-bullet-${this.activeIndex}`);
    activeSwiperBulletEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  stateOfCommanderBtn() {
    this.stateCommanderBtn = false;

    this.selectedOffre?.listeBlocs.map((item) => {
      if (item.etat === 'I') {
        this.stateCommanderBtn = true;
      }
    })
  }

  openConfirmationPage(page: string) {
    this.navController.navigateForward([page], {
      state: {
        selectedOffre: JSON.parse(JSON.stringify(this.selectedOffre)),
        savedCommande: JSON.parse(JSON.stringify(this.savedCommande ? this.savedCommande : this.selectedOffre))
      }
    });
  }

  async openConfirmationModal() {
    this.selectedClient && (this.selectedOffre['client'] = this.selectedClient);

    if (!!this.selectedClientLocal) {
      this.selectedOffre['codeClientLocal'] =
        (this.selectedClientLocal instanceof Object) ? this.selectedClientLocal?.code : this.selectedClientLocal;
    }

    const confirmationModal = await this.modalController.create({
      component: ConfirmationCommandePage,
      componentProps: {
        selectedOffre: this.selectedOffre,
        selectedClientLocal: this.selectedClientLocal,
        savedCommande: this.savedCommande ?? this.selectedOffre
      }
    });

    await confirmationModal.present();
  }


  openPageSwitch(page: string) {
    this.navController.navigateRoot([page], {});
  }

  handleChange(index: number) {
    this.stateOfCommanderBtn();
    this.isSaved = false;
    if (index >= 0) {
      this.currentChanges[index] = true;
    }
    // console.log('fffff change')
    // this.swiper.pagination.bullets.addClass('error-ls');
    this.swiper.pagination.render();
  }

  packHasNoneZeroQte(blocOffre: BlocOffre): boolean {
    for (let fils of blocOffre?.listeFils) {
      if (fils.qteCmd > 0) return true;

      if (fils?.listeFils.length > 0) {
        return this.packHasNoneZeroQte(fils);
      }
    }

    return false;
  }

  getClientLocal() {
    let codeLocal: number;
    const codeGroupe = this.selectedClient?.code;
    this.accesClientService.searchPharmacyByCode(codeGroupe)
      .pipe(
        map(pharmacies => pharmacies?.content[0]?.accesClient?.codeClientLocal),
        tap(codeL => codeLocal = codeL),
        switchMap(codeLocal => this.accesClientService.filterClientsLocal(codeLocal?.toString())),
        map(clientsFournisseur => clientsFournisseur[0])
      ).subscribe(res => {

        if (!!res) {
          this.selectedClientLocal = { ...res, estClient: true };
        } else {
          this.selectedClientLocal = codeLocal ?
            ({
              code: codeLocal,
              raisonSociale: codeLocal,
              estClient: true
            } as any) : res;
        }

        if (!this.selectedClientLocal && this.selectedOffre?.codeClientLocal && this.firstLoad) {
          this.selectedClientLocal = this.selectedOffre?.codeClientLocal as any;
        }

        this.firstLoad = false;
      });
  }

  openClientModal(val: { open: boolean; isClientLocal: boolean }): void {
    this.clientTypeaheadModalIsOpen = true;
    this.clientFournisseur = val?.isClientLocal;
  }

  ngOnDestroy(): void {
    this.backBtnSubscription.unsubscribe();
  }
}
