import { ProduitDTO } from "libs/commandes-web/commande/src/lib/models/produitDTO";
import { EntrepriseDTO } from "./entreprise.model";


interface IEchangeAchatGroupeDTO {
  id: number;
  clientDonneur : EntrepriseDTO;
  clientReceveur : EntrepriseDTO;
  entBlConsolideId;
  dateCreation: string;
  lignes: IDetailEchangeAchatGroupeDTO[];
}

interface IDetailEchangeAchatGroupeDTO {
  id : number;
  blocOffreId: number;
  produitDto: ProduitDTO;
  quantiteEchangee: number;
}


export class EchangeAchatGroupeDTO implements IEchangeAchatGroupeDTO {
  id: number;
  clientDonneur : EntrepriseDTO;
  clientReceveur : EntrepriseDTO;
  entBlConsolideId;
  dateCreation: string;
  lignes: IDetailEchangeAchatGroupeDTO[];
  constructor(partial: Partial<IEchangeAchatGroupeDTO>) {
    Object.assign(this, partial);
  }
}



export class DetailEchangeAchatGroupeDTO implements IDetailEchangeAchatGroupeDTO {
  id : number;
  blocOffreId: number;
  produitDto: ProduitDTO;
  quantiteEchangee: number;
  constructor(partial: Partial<IDetailEchangeAchatGroupeDTO>) {
    Object.assign(this, partial);
  }
}
