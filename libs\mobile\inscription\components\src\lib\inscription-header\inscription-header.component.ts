import {Component, Input, OnInit, ViewEncapsulation} from '@angular/core';
import {NavController} from "@ionic/angular";

@Component({
  selector: 'wph-inscription-header',
  templateUrl: './inscription-header.component.html',
  styleUrls: ['./inscription-header.component.scss'],
  encapsulation: ViewEncapsulation.Emulated,
})
export class InscriptionHeaderComponent implements OnInit {

  @Input() backToRoute: boolean;
  @Input() showBackBtn?: boolean = true;
  constructor(private navController: NavController) {}

  ngOnInit(): void {}


  goToLogin() {
    if(this.backToRoute) {
      this.navController.navigateRoot('')
    } else {
      this.navController.back();
    }
  }
}
