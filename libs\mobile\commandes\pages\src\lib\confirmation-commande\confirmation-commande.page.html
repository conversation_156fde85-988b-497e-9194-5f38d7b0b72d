<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="dismissModal()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{'Confirmation d\'une commande'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>

    <ng-container *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']">
      <div class="client-container">
        <ion-row class="ion-padding-vertical">
          <ion-col size="3" class="font-bold">Code</ion-col>
          <ion-col class="font-bold">Raison Sociale</ion-col>
          <ion-col class="font-bold">Ville</ion-col>
        </ion-row>
    
        <ion-row class="ion-padding-bottom">
          <ion-col size="3">
            <ion-label class="item-column">G{{ selectedOffre?.client?.code }}</ion-label>
          </ion-col>
          <ion-col>{{ selectedOffre?.client?.raisonSociale }}</ion-col>
          <ion-col>{{ selectedOffre?.client?.ville }}</ion-col>
        </ion-row> 
      </div>

      <div class="client-local-container">
        <ion-row class="ion-padding-vertical">
          <ion-col size="3" class="font-bold">Code</ion-col>
          <ion-col class="font-bold">Raison Sociale</ion-col>
          <ion-col class="font-bold">Ville</ion-col>
        </ion-row>
    
        <ion-row class="ion-padding-bottom">
          <ion-col size="3">
            <ion-label class="item-column">{{ $any(selectedClientLocal)?.code || selectedClientLocal }}</ion-label>
          </ion-col>
          <ion-col>{{ $any(selectedClientLocal)?.raisonSociale }}</ion-col>
          <ion-col>{{ $any(selectedClientLocal)?.ville }}</ion-col>
        </ion-row> 
      </div>
    </ng-container>

    <ion-row class="ion-padding-vertical">
      <ion-col size="3"></ion-col>
      <ion-col size="3" class="font-bold">Brut</ion-col>
      <ion-col size="3" class="font-bold">Net</ion-col>
      <ion-col size="3" class="font-bold">Remise</ion-col>

    </ion-row>
    <ion-row class="py-confirm" *ngFor="let bloc of selectedOffre?.listeBlocs; let i = index">
      <ion-col size="3" >Pack N°: {{ i + 1}}</ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">{{bloc.totalValeurBruteCmd | number: '1.2-2':'fr-FR'}}</ion-label></ion-col>
      <ion-col size="3">
        <ion-label class="item-column">{{bloc.totalValeurNetteCmd | number: '1.2-2':'fr-FR'}}</ion-label></ion-col>
      <ion-col size="3" >
        <ion-label class="item-column">{{( bloc.totalValeurBruteCmd - bloc.totalValeurNetteCmd) | number: '1.2-2':'fr-FR'}}</ion-label></ion-col>
    </ion-row>
<!--    <ion-row class="py-confirm">-->
<!--      <ion-col size="3" >Pack N°: 2</ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">120.30 DH</ion-label></ion-col>-->
<!--      <ion-col size="3">-->
<!--        <ion-label class="item-column">100.00 DH</ion-label></ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">20.30 DH</ion-label></ion-col>-->
<!--    </ion-row>-->
<!--    <ion-row class="py-confirm">-->
<!--      <ion-col size="3" >Pack N°: 3</ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">120.30 DH</ion-label></ion-col>-->
<!--      <ion-col size="3">-->
<!--        <ion-label class="item-column">100.00 DH</ion-label></ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">20.30 DH</ion-label></ion-col>-->
<!--    </ion-row>-->
<!--    <ion-row class="py-confirm">-->
<!--      <ion-col size="3" >Pack N°: 4</ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">120.30 DH</ion-label></ion-col>-->
<!--      <ion-col size="3">-->
<!--        <ion-label class="item-column">100.00 DH</ion-label></ion-col>-->
<!--      <ion-col size="3" >-->
<!--        <ion-label class="item-column">20.30 DH</ion-label></ion-col>-->
<!--    </ion-row>-->
  </ion-grid>
  <div class="cmd-cmd-footer-conf ion-no-padding">
    <ion-grid class="ion-no-padding">
      <ion-row class="footer-wrapper ion-padding-horizontal">
        <ion-col size="7" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-lg">Montant Total Net</ion-label>
           <ion-input [readonly]="true"  [value]="(selectedOffre?.totalValeurNetteCmd || 0) | number: '1.2-2':'fr-FR'" class="footer-inp-lg"></ion-input>
        </ion-col>
        <ion-col size="5" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-sm">Soit une remise</ion-label>
           <ion-input [readonly]="true" [value]="((selectedOffre?.totalValeurBruteCmd - selectedOffre?.totalValeurNetteCmd) || 0) | number: '1.2-2':'fr-FR'" class="footer-inp-sm"></ion-input>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
  <ion-grid>
    <ion-row class="">
      <ion-col size="12">Message : </ion-col>
      <ion-col size="12">
        <ion-textarea [(ngModel)]="message" placeholder="message...." class="message-box"></ion-textarea>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
<ion-footer>

  <div class="ion-flex ion-justify-content-between ion-p-action">
  <ion-button class="btn-actions valider-btn" (click)="dismissModal()">
    <ion-icon name="chevron-back-outline" class="mx-lg"></ion-icon>
    Retourner</ion-button>
    <ion-button class="btn-actions valider-btn" (click)="valider()">
      <ion-icon name="checkmark-done-outline" class="mx-lg"></ion-icon>
      Valider</ion-button>
  </div>
</ion-footer>
