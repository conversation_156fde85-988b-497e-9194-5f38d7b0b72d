# Fonctionnalité d'Échange entre Membres

## Vue d'ensemble

Cette fonctionnalité permet d'effectuer des échanges bidirectionnels de produits entre les membres d'un groupe lors de la répartition des bons de livraison (BL). Elle respecte le modèle `EchangeAchatGroupeDTO` et gère tous les cas d'usage possibles.

## Fonctionnalités Implémentées

### 1. Bouton d'Échange
- **Localisation** : Dans l'en-tête de la page de dispatch BL
- **Visibilité** : Affiché uniquement quand :
  - Le BL n'est pas encore validé (`!isAllValidated`)
  - Le statut du BL est 'BROUILLON' (`enteteBlConsolide?.etatBl === 'BROUILLON'`)
- **Icône** : `bi bi-arrow-left-right`
- **Texte** : "Échange"

### 2. Modal d'Échange
- **Titre** : "ÉCHANGE ENTRE MEMBRES"
- **Composants** :
  - Sélection du membre donneur (dropdown)
  - Sélection du membre receveur (dropdown)
  - Grille des produits disponibles pour l'échange
  - Boutons d'action (Annuler/Effectuer l'échange)

### 3. Sélection des Membres
- **Validation** : Les deux membres sont requis
- **Restriction** : Un membre ne peut pas être à la fois donneur et receveur
- **Affichage** : Format "Dr. [Nom du membre]"

### 4. Grille des Produits
- **Colonnes** :
  - Produit (désignation)
  - Qté Donneur (quantité disponible chez le donneur)
  - Qté Receveur (quantité actuelle chez le receveur)
  - Qté à échanger (input numérique)

- **Filtrage** : Seuls les produits avec une quantité disponible > 0 chez le donneur sont affichés

### 5. Validation des Échanges
- **Contrôles** :
  - Quantité à échanger ≤ quantité disponible chez le donneur
  - Quantité à échanger ≥ 0
  - Au moins un produit avec quantité > 0 doit être sélectionné

### 6. Traitement de l'Échange
- **Mise à jour des données** :
  - Réduction de la quantité chez le donneur
  - Augmentation de la quantité chez le receveur
  - Marquage des lignes comme modifiées
  - Recalcul des totaux des membres

### 7. Envoi au Backend
- **Service** : `FsBLService.createExchange()`
- **Logging** : Toutes les données sont loggées dans la console comme demandé
- **Modèle** : Utilise `EchangeAchatGroupeDTO` et `DetailEchangeAchatGroupeDTO`

## Structure des Données

### EchangeAchatGroupeDTO
```typescript
{
  id: number;
  clientDonneur: EntrepriseDTO;
  clientReceveur: EntrepriseDTO;
  entBlConsolideId: string;
  dateCreation: string;
  lignes: DetailEchangeAchatGroupeDTO[];
}
```

### DetailEchangeAchatGroupeDTO
```typescript
{
  id: number;
  blocOffreId: number;
  produitDto: ProduitDTO;
  quantiteEchangee: number;
}
```

## Cas d'Usage Gérés

### 1. Échange Simple
- Un membre donne une partie de sa quantité à un autre membre
- Les quantités sont mises à jour immédiatement dans l'interface

### 2. Échange Multiple
- Plusieurs produits peuvent être échangés en une seule transaction
- Chaque produit peut avoir une quantité différente

### 3. Validation des Contraintes
- Impossible d'échanger plus que la quantité disponible
- Impossible d'échanger des quantités négatives
- Validation des membres sélectionnés

### 4. Gestion des Erreurs
- Messages d'erreur appropriés pour chaque cas d'échec
- Validation côté client avant envoi

## Intégration avec l'Interface

### Menu Mobile
- Le bouton d'échange est également disponible dans le menu mobile
- Même logique de visibilité que le bouton desktop

### Mise à Jour de l'Interface
- Les changements sont immédiatement visibles dans la grille principale
- Les totaux des membres sont recalculés automatiquement
- Les cellules modifiées sont mises en surbrillance

## Logging et Débogage

Toutes les opérations d'échange sont loggées dans la console :
- Données de l'échange avant envoi
- Détails de chaque ligne d'échange
- Réponse du service
- Erreurs éventuelles

## Tests

Un fichier de tests complet est fourni (`bl-dispatch-exchange.test.ts`) couvrant :
- Validation du formulaire
- Création du DTO d'échange
- Application des changements aux données
- Gestion des erreurs
- Logging des données

## Utilisation

1. Ouvrir la page de dispatch d'un BL en statut 'BROUILLON'
2. Cliquer sur le bouton "Échange"
3. Sélectionner le membre donneur et le membre receveur
4. Saisir les quantités à échanger pour chaque produit
5. Cliquer sur "Effectuer l'échange"
6. Vérifier les modifications dans la grille principale

## Notes Techniques

- La fonctionnalité respecte les contraintes métier existantes
- Les échanges ne sont possibles que sur les BL non validés
- Toutes les modifications sont marquées pour sauvegarde ultérieure
- Le système est bidirectionnel : A peut donner à B et B peut donner à A
