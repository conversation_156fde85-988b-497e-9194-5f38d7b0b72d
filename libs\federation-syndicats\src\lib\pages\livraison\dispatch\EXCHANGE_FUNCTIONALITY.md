# Fonctionnalité d'Échange entre Membres

## Vue d'ensemble

Cette fonctionnalité permet d'effectuer des échanges bidirectionnels de produits entre les membres d'un groupe lors de la répartition des bons de livraison (BL). Elle respecte le modèle `EchangeAchatGroupeDTO` et gère tous les cas d'usage possibles.

## Fonctionnalités Implémentées

### 1. Bouton d'Échange
- **Localisation** : Dans l'en-tête de la page de dispatch BL
- **Visibilité** : Affiché uniquement quand :
  - Le BL n'est pas encore validé (`!isAllValidated`)
  - Le statut du BL est 'BROUILLON' (`enteteBlConsolide?.etatBl === 'BROUILLON'`)
- **Icône** : `bi bi-arrow-left-right`
- **Texte** : "Échange"

### 2. Modal d'Échange
- **Titre** : "ÉCHANGE ENTRE MEMBRES"
- **Composants** :
  - Sélection du membre donneur (dropdown)
  - Sélection du membre receveur (dropdown)
  - Grille des produits disponibles pour l'échange
  - Boutons d'action (Annuler/Effectuer l'échange)

### 3. Sélection des Membres
- **Validation** : Les deux membres sont requis
- **Restriction** : Un membre ne peut pas être à la fois donneur et receveur
- **Affichage** : Format "Dr. [Nom du membre]"

### 4. Grille des Produits
- **Colonnes** :
  - Produit (désignation)
  - Qté Donneur (quantité disponible chez le donneur)
  - Qté Receveur (quantité actuelle chez le receveur)
  - Qté à échanger (input numérique)

- **Filtrage** : Seuls les produits avec une quantité disponible > 0 chez le donneur sont affichés

### 5. Validation des Échanges
- **Contrôles** :
  - Quantité à échanger ≤ quantité disponible chez le donneur
  - Quantité à échanger ≥ 0
  - Au moins un produit avec quantité > 0 doit être sélectionné

### 6. Traitement de l'Échange
- **Mise à jour des données** :
  - Réduction de la quantité chez le donneur
  - Augmentation de la quantité chez le receveur
  - Marquage des lignes comme modifiées
  - Recalcul des totaux des membres

### 7. Envoi au Backend
- **Service** : `FsBLService.createExchange()`
- **Logging** : Toutes les données sont loggées dans la console comme demandé
- **Modèle** : Utilise `EchangeAchatGroupeDTO` et `DetailEchangeAchatGroupeDTO`

## Structure des Données

### EchangeAchatGroupeDTO
```typescript
{
  id: number;
  clientDonneur: EntrepriseDTO;
  clientReceveur: EntrepriseDTO;
  entBlConsolideId: string;
  dateCreation: string;
  lignes: DetailEchangeAchatGroupeDTO[];
}
```

### DetailEchangeAchatGroupeDTO
```typescript
{
  id: number;
  blocOffreId: number;
  produitDto: ProduitDTO;
  quantiteEchangee: number;
}
```

## Cas d'Usage Gérés

### 1. Échange Simple
- Un membre donne une partie de sa quantité à un autre membre
- Les quantités sont mises à jour immédiatement dans l'interface

### 2. Échange Multiple
- Plusieurs produits peuvent être échangés en une seule transaction
- Chaque produit peut avoir une quantité différente

### 3. Validation des Contraintes
- Impossible d'échanger plus que la quantité disponible
- Impossible d'échanger des quantités négatives
- Validation des membres sélectionnés

### 4. Gestion des Erreurs
- Messages d'erreur appropriés pour chaque cas d'échec
- Validation côté client avant envoi

## Intégration avec l'Interface

### Menu Mobile
- Le bouton d'échange est également disponible dans le menu mobile
- Même logique de visibilité que le bouton desktop

### Mise à Jour de l'Interface
- Les changements sont immédiatement visibles dans la grille principale
- Les totaux des membres sont recalculés automatiquement
- Les cellules modifiées sont mises en surbrillance

## Logging et Débogage

Toutes les opérations d'échange sont loggées dans la console :
- Données de l'échange avant envoi
- Détails de chaque ligne d'échange
- Réponse du service
- Erreurs éventuelles

## Tests

Un fichier de tests complet est fourni (`bl-dispatch-exchange.test.ts`) couvrant :
- Validation du formulaire
- Création du DTO d'échange
- Application des changements aux données
- Gestion des erreurs
- Logging des données

## Utilisation

1. Ouvrir la page de dispatch d'un BL en statut 'BROUILLON'
2. Cliquer sur le bouton "Échange"
3. Sélectionner le membre donneur et le membre receveur
4. Saisir les quantités à échanger pour chaque produit
5. Cliquer sur "Effectuer l'échange"
6. Vérifier les modifications dans la grille principale

## Interface Utilisateur Moderne

### Design System
- **Couleurs** : Palette cohérente avec Bootstrap 5
- **Typographie** : Hiérarchie claire avec poids de police appropriés
- **Espacement** : Système de grille responsive et espacement cohérent
- **Animations** : Transitions fluides et feedback visuel

### Composants UI

#### 1. En-tête Modal
- Icône contextuelle avec arrière-plan coloré
- Titre principal et sous-titre descriptif
- Bouton de fermeture moderne (btn-close)

#### 2. Indicateur de Progression
- Barre de progression visuelle pour les échanges en cours
- Badge avec compteur d'échanges
- Animation de progression fluide

#### 3. Cartes de Sélection des Membres
- Design en cartes avec en-têtes colorés
- Icônes distinctives pour donneur (warning) et receveur (success)
- Informations contextuelles sous les sélecteurs
- Effets de survol et focus

#### 4. Grille de Produits Moderne
- Tableau responsive avec en-têtes iconifiés
- Colonnes avec codes couleur :
  - **Donneur** : Orange/Warning
  - **Receveur** : Vert/Success
  - **Échange** : Bleu/Primary
  - **Disponible** : Cyan/Info
- Contrôles de quantité avec boutons +/-
- Validation visuelle en temps réel

#### 5. Résumé des Échanges en Attente
- Cartes d'échange avec avatars des membres
- Informations détaillées (produits, quantités)
- Bouton de suppression par échange
- Statistiques en temps réel

#### 6. Actions et Statistiques
- Boutons avec gradients et effets de survol
- Résumé statistique en pied de modal
- Compteurs en temps réel

## Validation Avancée des Quantités

### Validation Multi-Échanges
```typescript
// Calcul des quantités disponibles en tenant compte des échanges en attente
getMaxAvailableQuantity(product): number {
  const originalQuantity = product.quantiteDonneurDisponible;
  const usedInPendingExchanges = this.getQuantityUsedInPendingExchanges(
    product.blocOffreId,
    this.selectedDonneurId
  );
  return Math.max(0, originalQuantity - usedInPendingExchanges);
}
```

### Types de Validation
1. **Validation de Saisie** : Contrôle en temps réel lors de la saisie
2. **Validation Croisée** : Vérification entre échanges multiples
3. **Validation Visuelle** : Feedback immédiat avec couleurs et messages
4. **Validation Logique** : Respect des contraintes métier

### Messages d'Erreur Contextuels
- Messages spécifiques selon le type d'erreur
- Affichage des quantités maximales disponibles
- Prise en compte des échanges en attente
- Suggestions d'actions correctives

## Fonctionnalités Avancées

### Gestion des Échanges Multiples
- **Ajout Séquentiel** : Possibilité d'ajouter plusieurs échanges
- **Suivi en Temps Réel** : Mise à jour des disponibilités après chaque ajout
- **Suppression Individuelle** : Retrait d'échanges spécifiques avec revert
- **Validation Globale** : Vérification de cohérence avant finalisation

### Contrôles de Quantité Intelligents
- **Boutons +/-** : Incrémentation/décrémentation avec limites
- **Saisie Directe** : Input numérique avec validation
- **Limites Dynamiques** : Adaptation selon les échanges en cours
- **Feedback Visuel** : Couleurs et icônes selon l'état

### Statistiques et Résumés
- **Compteurs Temps Réel** : Nombre d'échanges, produits, quantités
- **Résumé par Échange** : Détails de chaque transaction
- **Progression Visuelle** : Barre de progression et badges
- **Historique** : Suivi des modifications

## Responsive Design

### Breakpoints
- **Mobile** : < 768px - Layout vertical, boutons pleine largeur
- **Tablet** : 768px - 1024px - Layout hybride
- **Desktop** : > 1024px - Layout complet avec toutes les colonnes

### Adaptations Mobile
- Cartes de sélection empilées verticalement
- Tableau responsive avec scroll horizontal
- Boutons d'action adaptés aux écrans tactiles
- Textes et icônes optimisés pour la lisibilité

## Accessibilité

### Standards WCAG
- **Contraste** : Ratios de couleur conformes
- **Navigation** : Support clavier complet
- **Lecteurs d'écran** : Labels et descriptions appropriés
- **Focus** : Indicateurs visuels clairs

### Améliorations UX
- **Feedback Haptique** : Vibrations sur mobile (si supporté)
- **Animations Réduites** : Respect des préférences utilisateur
- **Tailles de Cible** : Boutons suffisamment grands pour le tactile
- **Messages Clairs** : Langage simple et instructions précises

## Notes Techniques

- La fonctionnalité respecte les contraintes métier existantes
- Les échanges ne sont possibles que sur les BL non validés
- Toutes les modifications sont marquées pour sauvegarde ultérieure
- Le système est bidirectionnel : A peut donner à B et B peut donner à A
- Interface moderne avec Bootstrap 5 et CSS personnalisé
- Validation en temps réel avec gestion des états complexes
- Support complet des échanges multiples avec tracking des quantités
