import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AlertService } from '@wph/shared';
import { BLDispatchComponent } from './bl-dispatch.component';
import { FsBLService } from '../../../services/fs-bl.service';
import { EchangeAchatGroupeDTO, DetailEchangeAchatGroupeDTO } from '../../../models/echange.model';
import { EntrepriseDTO } from '../../../models/entreprise.model';
import { of } from 'rxjs';

describe('BLDispatchComponent - Exchange Functionality', () => {
  let component: BLDispatchComponent;
  let fixture: ComponentFixture<BLDispatchComponent>;
  let mockFsBLService: jasmine.SpyObj<FsBLService>;
  let mockAlertService: jasmine.SpyObj<AlertService>;
  let mockModalService: jasmine.SpyObj<NgbModal>;

  beforeEach(async () => {
    const fsBLServiceSpy = jasmine.createSpyObj('FsBLService', ['createExchange']);
    const alertServiceSpy = jasmine.createSpyObj('AlertService', ['error', 'successAlt']);
    const modalServiceSpy = jasmine.createSpyObj('NgbModal', ['open']);

    await TestBed.configureTestingModule({
      declarations: [BLDispatchComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: FsBLService, useValue: fsBLServiceSpy },
        { provide: AlertService, useValue: alertServiceSpy },
        { provide: NgbModal, useValue: modalServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(BLDispatchComponent);
    component = fixture.componentInstance;
    mockFsBLService = TestBed.inject(FsBLService) as jasmine.SpyObj<FsBLService>;
    mockAlertService = TestBed.inject(AlertService) as jasmine.SpyObj<AlertService>;
    mockModalService = TestBed.inject(NgbModal) as jasmine.SpyObj<NgbModal>;

    // Initialize component properties
    component.listMembers = new Set([
      { id: 1, nom: 'Dr. Ahmed' },
      { id: 2, nom: 'Dr. Fatima' }
    ]);
    
    component.gridData = {
      data: [
        {
          blocOffreId: 1,
          designation: 'Produit Test 1',
          produitDto: { id: 1, libelle: 'Produit Test 1' }
        },
        {
          blocOffreId: 2,
          designation: 'Produit Test 2',
          produitDto: { id: 2, libelle: 'Produit Test 2' }
        }
      ],
      total: 2
    };

    component.initExchangeForm();
  });

  it('should create exchange form with required validators', () => {
    expect(component.exchangeForm).toBeDefined();
    expect(component.exchangeForm.get('clientDonneur')).toBeDefined();
    expect(component.exchangeForm.get('clientReceveur')).toBeDefined();
  });

  it('should validate exchange form correctly', () => {
    // Initially invalid
    expect(component.isExchangeValid()).toBeFalsy();

    // Set form values
    component.exchangeForm.patchValue({
      clientDonneur: '1',
      clientReceveur: '2'
    });

    // Still invalid without exchange quantities
    expect(component.isExchangeValid()).toBeFalsy();

    // Add exchange data with quantities
    component.exchangeProductsData = [
      {
        blocOffreId: 1,
        designation: 'Produit Test 1',
        quantiteEchange: 5
      }
    ];

    // Now should be valid
    expect(component.isExchangeValid()).toBeTruthy();
  });

  it('should create exchange DTO correctly', () => {
    // Setup test data
    component.selectedDonneurId = 1;
    component.selectedReceveurId = 2;
    component.exchangeForm.patchValue({
      clientDonneur: '1',
      clientReceveur: '2'
    });
    component.exchangeProductsData = [
      {
        blocOffreId: 1,
        designation: 'Produit Test 1',
        produitDto: { id: 1, libelle: 'Produit Test 1' },
        quantiteEchange: 5
      }
    ];
    component.idBL = '123';

    // Mock service response
    mockFsBLService.createExchange.and.returnValue(of(new EchangeAchatGroupeDTO({})));

    // Mock modal
    const mockModal = { close: jasmine.createSpy('close') };

    // Process exchange
    component.processExchange(mockModal);

    // Verify service was called
    expect(mockFsBLService.createExchange).toHaveBeenCalled();
    expect(mockAlertService.successAlt).toHaveBeenCalled();
    expect(mockModal.close).toHaveBeenCalledWith('exchange-completed');
  });

  it('should show error when exchange is invalid', () => {
    const mockModal = { close: jasmine.createSpy('close') };
    
    // Process exchange without valid data
    component.processExchange(mockModal);

    // Verify error was shown
    expect(mockAlertService.error).toHaveBeenCalledWith(
      'Veuillez sélectionner au moins un produit à échanger avec une quantité valide.',
      'MODAL'
    );
    expect(mockModal.close).not.toHaveBeenCalled();
  });

  it('should update exchange products data when members change', () => {
    // Mock getBlUnitaireLigneByBlocId method
    spyOn(component, 'getBlUnitaireLigneByBlocId').and.returnValue({
      quantiteLivree: 10
    });

    component.selectedDonneurId = 1;
    component.selectedReceveurId = 2;

    component.updateExchangeProductsData();

    expect(component.exchangeProductsData.length).toBeGreaterThan(0);
    expect(component.exchangeProductsData[0].quantiteDonneurDisponible).toBe(10);
  });

  it('should apply exchange to data correctly', () => {
    // Setup test data
    const exchangeDto = new EchangeAchatGroupeDTO({
      id: 1,
      clientDonneur: new EntrepriseDTO({ id: 1 }),
      clientReceveur: new EntrepriseDTO({ id: 2 }),
      entBlConsolideId: '123',
      dateCreation: new Date().toISOString(),
      lignes: [
        new DetailEchangeAchatGroupeDTO({
          id: 1,
          blocOffreId: 1,
          quantiteEchangee: 5
        })
      ]
    });

    // Mock getBlUnitaireLigneByBlocId method
    const donneurData = { quantiteLivree: 10 };
    const receveurData = { quantiteLivree: 5 };
    
    spyOn(component, 'getBlUnitaireLigneByBlocId')
      .and.returnValues(donneurData, receveurData);
    spyOn(component, 'generateMembersTotals');

    component.applyExchangeToData(exchangeDto);

    // Verify quantities were updated
    expect(donneurData.quantiteLivree).toBe(5); // 10 - 5
    expect(receveurData.quantiteLivree).toBe(10); // 5 + 5
    expect(donneurData['changed']).toBe(true);
    expect(receveurData['changed']).toBe(true);
    expect(component.generateMembersTotals).toHaveBeenCalled();
  });

  it('should log exchange data to console', () => {
    spyOn(console, 'log');
    
    const exchangeDto = new EchangeAchatGroupeDTO({
      id: 1,
      clientDonneur: new EntrepriseDTO({ id: 1, nomResponsable: 'Dr. Ahmed' }),
      clientReceveur: new EntrepriseDTO({ id: 2, nomResponsable: 'Dr. Fatima' }),
      entBlConsolideId: '123',
      dateCreation: new Date().toISOString(),
      lignes: [
        new DetailEchangeAchatGroupeDTO({
          id: 1,
          blocOffreId: 1,
          produitDto: { libelle: 'Produit Test' },
          quantiteEchangee: 5
        })
      ]
    });

    mockFsBLService.createExchange.and.returnValue(of(exchangeDto));

    component.sendExchangeToBackend(exchangeDto);

    expect(console.log).toHaveBeenCalledWith('=== ÉCHANGE ENTRE MEMBRES ===');
    expect(console.log).toHaveBeenCalledWith('Données de l\'échange à envoyer au backend:');
    expect(mockFsBLService.createExchange).toHaveBeenCalledWith(exchangeDto);
  });
});
