.custom-page-title-terms {
  text-align: left;
  font: normal normal bold 28px/33px Arial;
  letter-spacing: 0px;
  color: #3DA5D9;
  opacity: 1;
  margin-top: 10px;
}

.container {
  padding: 20px;
  position: relative;
  height: 95%;
}


:host {
  line-height: 1.5;
  font-family: <PERSON><PERSON>;
}
.send-button-container {
  margin-top: 6px;
  margin-bottom: 6px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  img {
    width: 20px;
  }
.send-button {
  width: 80%;
}
}
.input {
  margin-bottom: 20px;
}
.error-messages {
  color: red;
  position: absolute;
  font-size: 12px;
  width: 100%;
  left: 0;
  text-align: center;
}
.terms {
  padding: 10px;

  border-radius: 9px;
  background: #FCFCFC 0% 0% no-repeat padding-box;
  overflow: scroll;
  max-height: calc(100% - -180px - 300px);
}
