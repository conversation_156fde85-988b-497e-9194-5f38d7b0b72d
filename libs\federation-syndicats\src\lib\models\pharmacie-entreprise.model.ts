import { User } from "@wph/shared";
import { GroupeEntreprise } from "./groupe-entreprise.model";
import { Pagination } from "@wph/data-access";

export class PharmacieEntreprise {
    id?: number;
    raisonSociale?: string;
    civilite?: string;
    codePostal?: string;
    email?: string;
    nomResponsable?: string;
    adresse?: string;
    adresse2?: string;
    localite?: string;
    ville?: string;
    adresseAr?: string;
    adresse2Ar?: string;
    clientCible : PharmacieEntreprise
    villeRc?: string;
    telephone?: string;
    gsm1?: string;
    gsm2?: string;
    numCin?: string;
    numIce?: string;
    numIf?: string;
    numRc?: string;
    numPatente?: string;
    numRib?: string;
    numInpe?: string;
    numCnss?: string;
    numCompte?: string;
    fax?: string;
    siteWeb?: string;
    longitude?: number;
    latitude?: number;
    createdAt?: Date;
    createdById?: User;
    updatedAt?: Date;
    updatedById?: User;
    deletedAt?: Date;
    deletedById?: User;
    whatsapp?: string

    code?: string;
    typeEntreprise?: string;
    segmentEntreprise?: string;
    isEnrolled?: boolean;
    groupeEntreprise?: GroupeEntreprise;
    dateAttachementGroupe?: Date;
    dateDetachementGroupe?: Date;
    dateActivationMembreGroupe?: Date;
    dateDesactivationMembreGroupe?: Date;
    statutMembreGroupe?: boolean;
    statutJuridique?: string;
    dateActivation?: Date;
    dateDesactivation?: Date;
    statutEntreprise?: boolean;
}

export interface SearchPharmacieEntreprise extends Pagination {
    content?: PharmacieEntreprise[];
}
