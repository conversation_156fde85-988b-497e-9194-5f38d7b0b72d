import { ConfirmationCommandePage } from './confirmation-commande/confirmation-commande.page';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ListCommandesPage } from "./list-commandes/list-commandes.page";
import { CommandePage } from "./commande/commande.page";
import { MesCommandesPage } from './mes-commandes/mes-commandes.page';
import { SyntheseCommandePage } from './synthese-commande/synthese-commande.page';

const routes: Routes = [
  {
    path: '',
    component: ListCommandesPage,
    pathMatch:'full'
  },
  {
    path: 'confirmation',
    component: ConfirmationCommandePage,
    pathMatch:'full'
  },
  {
    path: 'synthese',
    component: SyntheseCommandePage,
    pathMatch:'full'
  },
  {
    path: 'br',
    component: MesCommandesPage,
    pathMatch:'full',
    data: {statut: 'B'}
  },
  {
    path: 'tr',
    component: MesCommandesPage,
    pathMatch:'full',
    data: {statut: 'V'}
  },
  {
    path: 'an',
    component: MesCommandesPage,
    pathMatch:'full',
    data: {statut: 'A'}
  },
  {
    path: ':id',
    component: CommandePage,
    pathMatch:'full'
  },


];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MobileCommandesPagesRoutingModule { }
