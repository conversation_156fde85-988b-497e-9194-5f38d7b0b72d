<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button shape="round" (click)="readOnly ? back() : quitter()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>

    <ion-buttons *ngIf="readOnly && selectedOffre?.commandStatut === 'NOUVELLE'" slot="end">
      <ion-button shape="round" (click)="showAnnulerAction()">
        <ion-icon name="ellipsis-vertical"></ion-icon>
      </ion-button>
    </ion-buttons>

    <ion-title>{{'Commande'}}</ion-title>
  </ion-toolbar>

  <ion-grid class="fixed-extra-header" *ngIf="!firstLoad">
    <ion-row class="ion-no-padding ion-no-margin">
      <ion-col class="ion-no-padding ion-no-margin">
        <div class="commande-preview-pagination">
          <div class="swiper-pagination" #swiperPaginationWrapper></div>
        </div>
        <ion-icon name="chevron-back-outline" class="swiper-button-prev" (click)="prevPage()"></ion-icon>
        <ion-icon name="chevron-forward-outline" class="swiper-button-next" (click)="nextPage()"></ion-icon>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-header>

<ion-content>
  <div class="first-load-container" *ngIf="firstLoad">
    <ion-spinner></ion-spinner>
  </div>
  <ion-grid class="ion-no-padding" *ngIf="!firstLoad">
    <ion-row class="ion-no-padding">
      <ion-col size="12">
        <swiper (swiper)="setSwiperInstance($event)" (slideChange)="onSlideChange()" [config]="swiperConfig">
          <ng-template *ngFor="let item of items; let i = index" class="swiper-slide" swiperSlide>
            <wph-command-edit [hasAccess]="hasAccess" [readOnly]="readOnly" (changedValue)="handleChange(i)"
              (openClientModal)="openClientModal($event)" [offre]="selectedOffre" [client]="selectedClient"
              [clientLocal]="selectedClientLocal" [loading]="firstLoad" style="width: 100%"
              [item]="item"></wph-command-edit>
          </ng-template>
        </swiper>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- FOOTER WITH ACTION BUTTONS -->
  <div *ngIf="!firstLoad" class="cmd-footer"
    [ngClass]="{'cmd-footer-tr': (readOnly && selectedOffre?.commandStatut !== 'NOUVELLE')}">
    <ion-grid>
      <ion-row class="footer-wrapper">
        <ion-col size="7" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-lg">Montant Total Net</ion-label>
          <ion-input [readonly]="true" [value]="(selectedOffre?.totalValeurNetteCmd || 0) | number: '1.2-2':'fr-FR'"
            class="footer-inp-lg"></ion-input>
        </ion-col>
        <ion-col size="5" class="ion-flex ion-justify-content-between ion-align-items-center">
          <ion-label class="font-bold montant-total-net ion-text-wrap footer-label-sm">Soit une remise</ion-label>
          <ion-input [readonly]="true"
            [value]="((selectedOffre?.totalValeurBruteCmd - selectedOffre?.totalValeurNetteCmd) || 0) | number: '1.2-2':'fr-FR'"
            class="footer-inp-sm"></ion-input>
        </ion-col>
      </ion-row>
    </ion-grid>

    <div *ngIf="!readOnly" class="ion-flex ion-justify-content-evenly ion-p-action mab">

      <ion-button wphIonClick *ngIf="selectedOffre?.commandStatut === 'BROUILLON'"
        class="btn-actions cmd-btns btn-annuler" (debounceClick)="showAnnulerAction()" [debounceTime]="500">
        <ion-icon name="ellipsis-vertical-circle-outline" class="mx-sm"></ion-icon>
        Action
      </ion-button>

      <ion-button class="btn-actions cmd-btns" wphIonClick (debounceClick)="save()" [debounceTime]="500">
        <ion-icon name="save-outline" class="mx-sm"></ion-icon>
        Enregistrer
      </ion-button>

      <ion-button *jhiHasAnyAuthority="['ROLE_AGENT_POINT_VENTE', 'ROLE_ASSISTANT']" class="btn-actions cmd-btns"
        [ngClass]="{'cmd-btns-readOnly': stateCommanderBtn || selectedOffre?.totalQteCmd === 0}" wphIonClick
        [debounceTime]="500" (debounceClick)="openConfirmationModal()">
        <ion-icon name="cart-outline" class="mx-sm"></ion-icon>
        Commander
      </ion-button>

      <ion-button *jhiHasAnyAuthority="['ROLE_AGENT_FOURNISSEUR', 'ROLE_AGENT_COMMERCIAL']" class="btn-actions cmd-btns"
        [ngClass]="{'cmd-btns-readOnly': stateCommanderBtn || selectedOffre?.totalQteCmd === 0 || !selectedClient}"
        wphIonClick [debounceTime]="500" (debounceClick)="openConfirmationModal()">
        <ion-icon name="cart-outline" class="mx-sm"></ion-icon>
        Commander
      </ion-button>
    </div>

    <div *ngIf="readOnly && selectedOffre?.commandStatut === 'NOUVELLE'"
      class="ion-flex ion-justify-content-evenly ion-p-action">
      <ion-button wphIonClick class="btn-actions cmd-btns btn-annuler" (debounceClick)="showAnnulerAction()"
        [debounceTime]="300">
        <ion-icon name="ellipsis-vertical-circle-outline" class="mx-sm"></ion-icon>
        Action
      </ion-button>
    </div>
  </div>
</ion-content>

<ion-modal (willDismiss)="(clientTypeaheadModalIsOpen = false); (selectedClient && swiper.pagination.render())"
  [isOpen]="clientTypeaheadModalIsOpen && !clientFournisseur">
  <ng-template>
    <wph-client-typeahead [(ngModel)]="selectedClient" (ngModelChange)="getClientLocal()"></wph-client-typeahead>
  </ng-template>
</ion-modal>

<ion-modal (willDismiss)="(clientTypeaheadModalIsOpen = false); (selectedClient && swiper.pagination.render())"
  [isOpen]="clientTypeaheadModalIsOpen && clientFournisseur">
  <ng-template>
    <wph-client-typeahead [(ngModel)]="selectedClientLocal"
      [isClientFournisseur]="clientFournisseur"></wph-client-typeahead>
  </ng-template>
</ion-modal>