<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <div class="d-flex  align-items-center col-auto k-gap-4">
            <button class="actions-icons action-back btn text-white" (click)="back()">
                <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
            </button>

            <h4 class="page-title fw-4 ps-2">Paramétrer groupe</h4>
        </div>

        <div class="col-auto px-0 mx-0">
            <div class="row d-flex justify-content-end m-0 p-0">
                <button (click)="back()" type="button" style="padding-block: 6px;"
                    class="btn btn-sm btn-dark text-white my-1 mx-0">
                    <i class="mdi mdi-close"></i>
                    Quitter
                </button>
            </div>
        </div>
    </div>
</div>


<div class="row d-flex m-0 px-2 py-1">
    <div class="card m-0 w-100 p-0 bg-white" [style.minHeight]="'calc(100vh - 80px)'">
        <div class="card-header p-0">
            <div class="row m-0 p-0">
                <div
                    class="col-auto col-md-8 col-xl-7 bg-card-header d-flex justify-content-between align-items-center">
                    <input #largeInput [value]="managedGroupe?.raisonSociale" [readOnly]="true" type="text"
                        placeholder="Nom du groupe ici" />
                </div>

                <div class="col col-md-4 col-xl-5 px-0 d-flex align-items-center">
                    <div class="row px-0 w-100 d-flex justify-content-end">
                        <div *ngIf="managedGroupe?.statutEntreprise" class="status-badge">
                            {{'Actif' | uppercase}}
                        </div>

                        <div *ngIf="!managedGroupe?.statutEntreprise" class="status-badge-inactif">
                            {{'Inactif' | uppercase}}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body pt-4 pb-1 px-2">
            <div class="row flex-wrap">

                <div class="col-md-6 mt-md-0 col-12 mt-2">
                    <label for="ville" class="form-label">{{ 'Ville du groupe' }}</label>

                    <div class="input-group">
                        <input type="text" [readOnly]="true" class="form-control form-control-md" id="ville"
                            [value]="managedGroupe?.ville" />
                    </div>
                </div>

                <div class="col-md-6 mt-md-0 col-12 mt-2">
                    <label for="localite" class="form-label">{{ 'Localité du groupe' }}</label>

                    <div class="input-group">
                        <input type="text" [readOnly]="true" class="form-control form-control-md" id="localite"
                            [value]="managedGroupe?.localite" />
                    </div>
                </div>
            </div>

            <div id="gestion-membre-container" class="row mt-4">
                <div class="card w-100" [style.minHeight]="'calc(100vh - 275px)'">
                    <ng-container *ngIf="(plateformeAsync$ | async) === 'WIN_GROUPE'; else: saisieMembreGridTemplate">
                        <ul ngbNav #produitsCommandeNav="ngbNav" [(activeId)]="activeTabIndex" class="nav-tabs">
                            <li ngbNavItem="0">
                                <a ngbNavLink class="d-flex align-items-center justify-content-center"
                                    [style.minWidth]="'280px'">
                                    <i class="mdi mdi-account-multiple mdi-24px mr-1"></i>
                                    {{ 'Gestion des membres' | uppercase }}
                                </a>
    
                                <ng-template ngbNavContent>
                                    <ng-container [ngTemplateOutlet]="saisieMembreGridTemplate"></ng-container>
                                </ng-template>
                            </li>
    
                            <li ngbNavItem="1">
                                <a ngbNavLink class="d-flex align-items-center justify-content-center"
                                    [style.minWidth]="'280px'">
                                    <i class="mdi mdi-cogs mdi-24px mr-1"></i>
                                    {{ 'Paramètrage du groupe' | uppercase }}
                                </a>
                                <ng-template ngbNavContent>
                                    <ng-container [ngTemplateOutlet]="parametrageGroupeGridTemplate"></ng-container>
                                </ng-template>
                            </li>
                        </ul>
    
                        <div [ngbNavOutlet]="produitsCommandeNav"></div>
                    </ng-container>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #indisponible>
    <span class="d-flex align-items-center justify-center k-gap-1 text-warning">
        <i class="bi bi-shield-fill-exclamation"></i>
        Indisponible
    </span>
</ng-template>


<ng-template #selectionnerChef let-modal>
    <div class="modal-header">
        <h4 class="modal-title text-dark" id="modal-basic-title">{{ 'Ajouter un membre du groupe' | uppercase }}</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div class="modal-body">
        <form class="mb-2" [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()">
            <div class="row flex-wrap">
                <div class="col-xl col-3 px-1">
                    <label for="nomResponsable" class="form-label">Nom Responsable</label>

                    <div class="input-group">
                        <input type="text" class="form-control form-control-md" id="nomResponsable"
                            formControlName="nomResponsable" />
                    </div>
                </div>

                <div class="col-xl col-3 px-1">
                    <label for="raisonSociale" class="form-label">Raison Sociale</label>

                    <div class="input-group">
                        <input type="text" class="form-control form-control-md" id="raisonSociale"
                            formControlName="raisonSociale" />
                    </div>
                </div>

                <div class="col-xl col-3 px-1">
                    <label for="ville" class="form-label">Ville</label>

                    <div class="input-group picker-input">
                        <input type="text" [readOnly]="readOnly" class="form-control form-control-md pl-4" id="ville"
                            formControlName="ville" [ngbTypeahead]="searchVilleOuLocalite"
                            [inputFormatter]="villeFormatter" [resultTemplate]="searchVilleTemplate" />

                        <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>

                        <ng-template #searchVilleTemplate let-result="result">
                            <b>{{ result?.labelFr }}</b>
                        </ng-template>
                    </div>
                </div>

                <div class="col-xl col-3 px-1">
                    <label for="localite" class="form-label">Localité</label>

                    <div class="input-group">
                        <input type="text" [readOnly]="readOnly" class="form-control form-control-md" id="localite"
                            formControlName="localite" />
                    </div>
                </div>

                <div class="col-xl col-3 mt-1 pt-0 pb-0 px-0">
                    <label class="col-sm-6 form-label p-0 ml-2" for="selectstatut"
                        style="margin-bottom: 0; margin-top: -2px">Statut</label>

                    <div class="input-group">
                        <select2 id="selectstatut" formControlName="statutMembreEntreprise"
                            [data]="membreStatutLabelsValues" hideSelectedItems="false" class="form-control-sm w-100"
                            multiple="false"></select2>

                    </div>
                </div>

                <div class="col-xl-auto col-3 px-1 d-flex align-items-end">
                    <div class="row d-flex align-items-end justify-content-center py-0">
                        <button type="button" (click)="viderFiltre()" title="Vider"
                            class="btn btn-sm btn-outline-primary b-radius">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>

                        <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                        </button>
                    </div>

                </div>
            </div>
        </form>

        <div class="row p-1 table-container" *ngIf="filterResult && filterResult.length > 0; else noResults"
            scrollListener>
            <div (click)="selectItem(result)" class="table-row w-100" *ngFor="let result of filterResult" [ngStyle]="{'background': result?.groupeEntreprise ? 'var(--fs-chef-bg)' : 
                currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-secondary-tint-2)'}">

                <span class="table-cell">
                    <i class="mdi mdi-account-circle mdi-18px"></i>
                    <span class="mx-1">Dr. {{ result?.nomResponsable }}</span>
                </span>

                <span class="table-cell">
                    <i class="bi bi-shop" style="font-size: 18px; margin-right: 3px;"></i>
                    <b>PH. {{ result?.raisonSociale }}</b>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-map-marker-radius mdi-18px"></i>
                    <span class="mx-1">{{ result?.ville }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-email mdi-18px"></i>
                    <span class="mx-1">{{ result?.email ?? 'Email indisponible' }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-phone mdi-18px"></i>
                    <span class="mx-1">{{ result?.gsm1 ?? 'GSM indisponible' }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-phone mdi-18px"></i>
                    <span class="mx-1">{{ result?.telephone ?? 'Tél indisponible' }}</span>
                </span>

                <span class="table-cell"
                    [ngStyle]="{'color': !result?.statutMembreGroupe ? 'var(--fs-success)' : 'black'}">
                    <i class="mdi mdi-account-multiple mdi-18px"></i>
                    <span class="mx-1">{{ (result?.statutMembreGroupe || result?.groupeEntreprise) ? 'Déjà Membre: ' +
                        result?.groupeEntreprise?.raisonSociale :
                        "Disponible"
                        }}</span>
                </span>

            </div>
        </div>
        <ng-template #noResults>
            <div class="w-100 text-center py-4">
                <p *ngIf="searchTerm">Aucun résultat trouvé pour '{{ searchTerm }}'.</p>
                <p *ngIf="!searchTerm">Aucun résultat trouvé.</p>
                <button *ngIf="searchTerm" (click)="suggestPharmacyAndCloseModal()" class="btn btn-md btn-primary"
                    title="Ajouter un produit" style="padding-block: 6px;  border-radius: 10px;">
                    <i class="mdi mdi-assistant"></i>
                    Suggérer '{{ searchTerm }}' comme pharmacie
                </button>
            </div>
        </ng-template>
    </div>
</ng-template>

<ng-template #saisieMembreGridTemplate>
    <div class="row d-flex w-100 p-0 m-0">
        <kendo-grid [data]="gridData" [rowClass]="rowClass" class="fs-grid" [pageable]="true" style="height: 100%"
            [skip]="navigation.skip" [pageSize]="navigation.pageSize" [resizable]="true">

            <ng-template kendoGridToolbarTemplate>
                <div class="row bg-white d-flex justify-content-between w-100 p-0">
                    <div
                        class="col-lg col-12 px-2 d-flex align-items-center justify-content-lg-start justify-content-center">
                        <button (click)="openModal(selectionnerChef)" type="button"
                            class="btn cstm-btn btn-md btn-primary">
                            <i class="mdi mdi-account-plus"></i>
                            Ajouter Membre
                        </button>
                    </div>

                    <div class="col-lg col-12 p-0 d-flex justify-content-end">
                        <div class="row w-100 p-0 d-flex justify-content-end">
                            <div class="col py-0 px-lg-0 px-1 m-1">
                                <div class="input-group picker-input">
                                    <input [formControl]="filterSearch" type="text" placeholder="Nom pharmacien(ne)"
                                        [style.minWidth]="'200px'" class="form-control form-control-md pl-4"
                                        id="groupeCritere" />

                                    <div class="picker-icons picker-icons-alt">
                                        <i class="mdi mdi-magnify pointer"></i>
                                    </div>
                                </div>
                            </div>

                            <button type="button" class="btn btn-sm search-btn m-1"
                                (click)="displayFilter = !displayFilter">
                                <i class="bi bi-sliders"></i>
                                Recherche Avancée
                            </button>

                            <app-export-pdf [exportRef]="exportPdfRef"></app-export-pdf>
                        </div>
                    </div>
                </div>
                <div class="row bg-white d-flex w-100 px-2 py-0">
                    <form [formGroup]="gridFilterForm" (ngSubmit)="appliquerGridFiltre()">
                        <div id="advanced-search" *ngIf="displayFilter" class="row p-0 flex-wrap px-1 my-2 gap-1">
                            <div class="col p-0 m-0">
                                <label for="raisonSociale" class="col-12 col-form-label text-left">Raison
                                    sociale</label>

                                <div class="col-12 input-group">
                                    <input type="text" name="raisonSociale" formControlName="raisonSociale"
                                        class="form-control form-control-md b-radius bg-white" id="raisonSociale">
                                </div>
                            </div>

                            <div class="col p-0 m-0">
                                <label for="ville" class="col-12 col-form-label text-left">Ville</label>

                                <div class="input-group picker-input">
                                    <input type="text" name="ville" formControlName="ville"
                                        class="form-control pl-4 form-control-md b-radius bg-white" id="ville"
                                        [ngbTypeahead]="searchVilleOuLocalite" [inputFormatter]="villeFormatter"
                                        [resultTemplate]="searchVilleTemplate">

                                    <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i>
                                    </div>

                                    <ng-template #searchVilleTemplate let-result="result">
                                        <b>{{ result?.labelFr }}</b>
                                    </ng-template>
                                </div>
                            </div>

                            <div class="col p-0 m-0">
                                <label for="localite" class="col-12 col-form-label text-left">Localité</label>

                                <div class="col-12 input-group">
                                    <input type="text" name="localite" formControlName="localite"
                                        class="form-control form-control-md b-radius bg-white" id="localite">
                                </div>
                            </div>
                            <div class="col mt-1 pt-1 pb-0 px-0">
                                <label class="col-sm-6 form-label p-0 ml-2" for="etatSuggestion"
                                    style="margin-bottom: -4px;">Statut</label>

                                <div class="input-group">
                                    <select2 id="etatSuggestion" formControlName="statutMembreEntreprise"
                                        [data]="stautsLabelsValues" hideSelectedItems="false"
                                        class="form-control-sm w-100" multiple="false">
                                    </select2>

                                </div>
                            </div>


                            <div class="col d-flex align-items-end py-0">
                                <button type="button" class="btn btn-sm btn-outline-primary b-radius" (click)="vider()">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>

                                <button type="submit" class="btn btn-sm btn-primary b-radius mx-1">
                                    <i class="mdi mdi-filter"></i>
                                    <span class="mx-1">Appliquer</span>
                                </button>
                            </div>
                        </div>
                    </form>

                </div>
            </ng-template>

            <kendo-grid-column title="Raison Sociale" class="text-wrap" [width]="180">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.raisonSociale; else: indisponible">{{
                        dataItem?.raisonSociale
                        }}</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column title="Pharmacien(ne)" class="text-wrap" [width]="150">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.nomResponsable; else: indisponible">Dr. {{
                        dataItem?.nomResponsable }}</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column title="Ville / Localité" class="text-wrap" [width]="150">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.ville; else: indisponible">{{ dataItem?.ville
                        }}</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="email" title="Email" class="text-wrap" [width]="150">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.email; else:indisponible">{{ dataItem?.email
                        }}</span>
                </ng-template>

            </kendo-grid-column>

            <kendo-grid-column field="gsm1" title="GSM" class="text-wrap" [width]="120">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.gsm1; else: indisponible">{{ dataItem?.gsm1
                        }}</span>
                </ng-template>

            </kendo-grid-column>

            <kendo-grid-column field="telephone" title="Téléphone" class="text-wrap" [width]="120">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span *ngIf="dataItem?.telephone; else: indisponible">{{
                        dataItem?.telephone }}</span>
                </ng-template>

            </kendo-grid-column>

            <kendo-grid-column field="dispo" title="Statut" [width]="80">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <div class="d-flex justify-content-center align-items-center">
                        <span *ngIf="!dataItem?.statutMembreGroupe" class="badge badge-grey rounded-pill py-1 px-2">
                            {{'Inactif' | uppercase}}
                        </span>

                        <span *ngIf="dataItem?.statutMembreGroupe" class="badge badge-success rounded-pill py-1 px-2">
                            {{'Actif' | uppercase}}
                        </span>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column title="Action" [width]="100">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                    <div class="d-flex justify-content-center k-gap-2">

                        <span (click)="suggererPharmacie(dataItem?.id)"
                            class="actions-icons  action-success  pointer-cus" title="Suggérer les modifications">
                            <i class="bi bi-pencil-square"></i>
                        </span>

                        <span (click)="((rowIndex > 0) && !dataItem?.statutMembreGroupe) && detacherMembre(dataItem)"
                            [ngClass]="{ 'opacity-light': dataItem?.statutMembreGroupe}"
                            class="actions-icons action-danger pointer-cus" title="Détacher membre">
                            <i class="bi bi-trash"></i>
                        </span>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

            <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage" let-total="total">
                <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"
                    [navigation]="navigation" style="width: 100%;"
                    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
            </ng-template>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Veuillez effectuer une recherche</span>
            </ng-template>
        </kendo-grid>
    </div>
</ng-template>

<ng-template #parametrageGroupeGridTemplate>
    <div class="row d-flex px-0 py-1 m-0 w-100">
        <kendo-grid [data]="gridDataResult" class="fs-grid" [pageable]="false" style="height: 100%" [resizable]="true">
            <kendo-grid-column field="libelle" title="Libellé du Paramètre" [width]="280" class="text-wrap">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span>{{ dataItem?.libelle }}</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="libelle" title="Description du Paramètre" class="text-wrap">
                <ng-template kendoGridCellTemplate let-dataItem>
                    <span>{{ dataItem?.libelle | flagDescription }}</span>
                </ng-template>
            </kendo-grid-column>

            <kendo-grid-column field="enabled" title="Statut" class="text-center" [width]="160">
                <ng-template kendoGridHeaderTemplate>
                    <span class="d-flex align-items-center justify-content-center w-100">Statut</span>
                </ng-template>

                <ng-template kendoGridCellTemplate let-dataItem>
                    <div (click)="activerOuDesactiverParametreGroupe(dataItem)"
                        class="d-flex justify-content-center align-items-center">
                        <kendo-switch size="medium" onLabel="Actif" [readonly]="true" [checked]="dataItem?.enabled"
                            offLabel="Inactif" class="status-switch-align">
                        </kendo-switch>
                    </div>
                </ng-template>
            </kendo-grid-column>

            <ng-template kendoGridNoRecordsTemplate>
                <span>Pas de paramètres</span>
            </ng-template>
        </kendo-grid>
    </div>
</ng-template>