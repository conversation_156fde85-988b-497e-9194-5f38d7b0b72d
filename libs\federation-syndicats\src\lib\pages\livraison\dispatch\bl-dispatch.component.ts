import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CellClickEvent, GridDataResult, RowClassArgs } from "@progress/kendo-angular-grid";
import { Fournisseur, Offre } from "@wph/data-access";
import { AlertService } from "@wph/shared";
import { DeferredActionButtonsService, ExportPdf, ExportPdfService, UserInputService } from "@wph/web/shared";
import * as moment from "moment";
import { forkJoin } from "rxjs";
import { DetailBlAchatGroupeDTO, EnteteBlConsolideeMarcheDTO, EnteteBlUnitaireMarcheDTO } from "../../../models/bl.model";
import { FederationSyndicatService } from "../../../services/federation-syndicats.service";
import { FsBLService } from "../../../services/fs-bl.service";
import { aggregateBy } from "@progress/kendo-data-query";

enum PageMode {
  SAISIE = 'S',
  P = 'P'
}


@Component({
    selector: 'wph-bl-dispatch',
    templateUrl: './bl-dispatch.component.html',
    styleUrls: ['./bl-dispatch.component.scss']
})
export class BLDispatchComponent implements OnDestroy {


    pageViewModel: any = {};

    @ViewChild('ChoseBLUnitaire') ChoseBLUnitaire: ElementRef<HTMLElement>;
    @ViewChild('ModalShowMoreMembers') ModalShowMoreMembers: ElementRef<HTMLElement>;


  @ViewChild('BLNameEDIT') BLnameInput: ElementRef<HTMLInputElement>;

    idBL:string;
    BLName ='Informations de Livraison';
    idCommande:number;
    enableBLNameEdit = false;
    saisieBLForm:FormGroup;
    listMembers:Set<{id:number,nom:string}> = new Set<{id:number,nom:string}>();
    isLoading = false;

    precomputedData: Map<string, any>;

    membersHasNoEmail = [];

    memeberSummary = new Map<any,{quantiteLivree:number,quantiteUg:number,quantiteCommandee:number, coffretCommandee?: number}>();

    isCoffretEnabled: boolean;

    enteteBlConsolide:EnteteBlConsolideeMarcheDTO;
    enteteBlUnitaire:EnteteBlUnitaireMarcheDTO[];

    gridData: GridDataResult = {
      data:[ ],
      total:0
    }

    selectedKeys: any[] = [];


    shouldShowmodalAfterLoad = false;


    filter = '';


  pageMode :PageMode = PageMode.SAISIE
  exportPdfRef: ExportPdf<any>;
  isAllValidated = false;
  notDispatched: boolean;

  precomputeData() {
    this.precomputedData = new Map();
    this.enteteBlUnitaire.forEach(bl => {
      bl.lignes.forEach(ligne => {
        const key = `${ligne.blocOffreId}-${bl.enteteCommandeAchatGroupe.client.id}`;
        this.precomputedData.set(key, ligne);
      });
    });
    this.generateMembersTotals();
    this.isLoading = false;
  }

    constructor(
      private router:Router,
      private alertService: AlertService,
      private userInputService: UserInputService,
      private fsBLService:FsBLService,
      private route: ActivatedRoute,
      private exportPdfServ: ExportPdfService,
      private fsService: FederationSyndicatService,
      private fb: FormBuilder,
      private modalService: NgbModal,
      private deferredActionBtnService: DeferredActionButtonsService,
    ) {
      this.route.params.subscribe(async params => {
        this.idBL = params['id'];
        if (this.idBL) {
          this.initSaisieBLForm();
          await this.getDispatchInfo();
        } else {
          this.pushMobileMenuOptions();
        }

        this.buildExport();
      });

      this.route.queryParams.subscribe(async params => {
        if (params['action'] == 'bls-envoyes') {
          this.shouldShowmodalAfterLoad = true;
        }
      });
    }


    formatter = (result: Fournisseur) => result?.raisonSociale;
    formatter2 = (result: Fournisseur) => result?.raisonSociale;

    commandFormatter  = (result: Offre) => result?.codeCommande;



    reInit() {
      this.initSaisieBLForm();
      this.getDispatchInfo();
      this.listMembers.clear();
      this.enableBLNameEdit = false;
      this.isLoading = false;
      this.isAllValidated = false;

    }




    initSaisieBLForm() {


      this.saisieBLForm = this.fb.group({
        id:[null],
        fournisseur: [null, Validators.required],
        numeroBl:[null, Validators.required],
        cumulBl:[0, Validators.required],
        montantSaisi: [0, Validators.required],
        tauxRf:[0, Validators.required],
        transporteur: [null, Validators.required],
        dateReceptionBl:[null, Validators.required],
        codeCommande:[null],
        enteteCommandeAchatGroupe:[null, Validators.required],
       })
  }



    onTabChange(ev:any){
        this.enableBLNameEdit = false;
    }

    enableBLNameEditFn() {
      this.enableBLNameEdit = true;


    }


    fillFromBL(blconsolide:EnteteBlConsolideeMarcheDTO){

        this.saisieBLForm.patchValue({
        id: blconsolide.id,
        fournisseur: blconsolide.fournisseur.raisonSociale,
        numeroBl: blconsolide.numeroBl,
        cumulBl: blconsolide.cumulBl,
        montantSaisi: blconsolide.montantSaisi,
        tauxRf: blconsolide.tauxRf,
        transporteur: blconsolide.raisonSocialeTransporteur,
        codeCommande: blconsolide.codeCommande,
        enteteCommandeAchatGroupe: blconsolide.enteteCommandeAchatGroupe.codeCommande,
        dateReceptionBl:moment(blconsolide.dateReceptionBl).isValid() ? moment(blconsolide.dateReceptionBl).format('DD/MM/YYYY') :null,
      });
      this.saisieBLForm.disable();

    }

    buildExport(): void {
      this.exportPdfRef = this.exportPdfServ.ref()
      .setTitle('Liste des produits de Le BL BL555')
      .addColumn('name', 'Désignation',{})
      .addColumn('qteCommande', 'Qté Commandé',{type: 'integer'})
      .addColumn('qteLivre', 'Qté Livré',{type: 'integer'})
      .addColumn('pph', 'PPH',{  type: 'decimal'})
      .addColumn('ppv', 'PPV',{  type: 'decimal'})
      .addColumn('remise', 'Remise',{  type: 'decimal'})
      .addColumn('montant', 'Montant',{  type: 'decimal'})
      .addColumn('dateper', 'Date peromption',{})

      .setData([])
  }

 async getDispatchInfo() {
    const groupe =  await this.fsService.getMyGroupe()
    const groupeId = groupe.id;
    const BLConsolide =  this.fsBLService.getBLConsolideById({blConsolideId: this.idBL,groupeId});
    const blsUnitaires =  this.fsBLService.getBLUnitairesByBLConsolideId({blConsolideId: this.idBL});
    this.isLoading = true;
    forkJoin([BLConsolide,blsUnitaires]).subscribe({
      next: ([blconsolide,blsUnitaires]) => {
        this.fillFromBL(blconsolide);
        this.enteteBlConsolide = blconsolide;
        this.gridData.data = this.enteteBlConsolide.lignes;

        if (this.enteteBlConsolide?.lignes?.length) {
          this.isCoffretEnabled = !!this.enteteBlConsolide.lignes[0].qteFixePrdInCoffret;
        }

        this.enteteBlUnitaire = blsUnitaires;
        this.isAllValidated = this.enteteBlUnitaire.every(bl => bl.etatBl === 'VALIDE');
        this.setupDispatchModel();
        this.generateTotals()
        this.generateMembersTotals()
        if(blsUnitaires.length && this.shouldShowmodalAfterLoad && this.isAllValidated){
          this.showModalForDispatch();
        }

        this.precomputeData();
        if(!blsUnitaires.length){
          if(blconsolide.enteteCommandeAchatGroupe.prioriteDispatchBl === true){
            this.dispatchMode('P');
          }else if(blconsolide.enteteCommandeAchatGroupe.prioriteDispatchBl === false){
            this.dispatchMode('S');
          }else{
            this.notDispatched = true;
          }
        }
      },
      complete: () => {
        this.pushMobileMenuOptions();
      }
    });
  }



  setupDispatchModel() {
    this.remplirListeMembers();

  }
  getBlUnitaireLigneByBlocId(blockId: number, memberId: number) {
    const key = `${blockId}-${memberId}`;
    return this.precomputedData?.get(key) || { quantiteCommandee: 0, quantiteLivree: 0, quantiteUg: 0, quantiteUgCmd: 0 };
  }

  async dispatchMode(mode:'P'|'S'){
    const groupe =  await this.fsService.getMyGroupe()
    const groupeId = groupe.id;
    const DispatchBL =  this.fsBLService.dispatchBLConsolide({ blConsolideId: `${this.idBL}`,groupeId,prioriserLeMoinsCommande : mode === 'P'});
    DispatchBL.subscribe(res => {
      this.enteteBlUnitaire = res;
      this.setupDispatchModel();
      this.isAllValidated = this.enteteBlUnitaire.every(bl => bl.etatBl === 'VALIDE');
      this.notDispatched = false;
      this.precomputeData();
    });

  }

  OnSelectionChange(event:CellClickEvent){

    if(event.columnIndex === 9){
      return;
    }
    const targetBl = event.dataItem.id;
    this.router.navigateByUrl(`/achats-groupes/bons-livraison/edit/${targetBl}/unitaire?from=dispatch&blc=${this.idBL}`);
    this.modalService.dismissAll();
  }



  remplirListeMembers() {
   this.enteteBlUnitaire.map(bl => {
       this.listMembers.add({id: bl.enteteCommandeAchatGroupe.client.id,nom :bl.enteteCommandeAchatGroupe.client.nomResponsable});
    });
  }


  getMemberbyIdfromSet(id:number){
   for (const member of this.listMembers) {
      if(member.id === id){
        return member;
      }
    }
    return null;
  }


  fieldHasChanged(blocOffreId,memberId,field){
    const bl = this.getBlUnitaireLigneByBlocId(blocOffreId,memberId);
    return bl?.changeField === field ;
  }



  updateFieldInDetail(blocOffreId:number,memberId:number,field:string,event){
    const bl = this.getBlUnitaireLigneByBlocId(blocOffreId,memberId);
     if(field === 'quantiteLivree'){
      // if(bl.quantiteLivree > bl.quantiteCommandee){
      //   bl.quantiteLivree = 0;
      //   event.target.value = 0;
      //   const member = this.getMemberbyIdfromSet(memberId);
      //   this.alertService.error("La quantité livrée ne peut pas être supérieure à la quantité commandée de member " + member?.nom,  'MODAL')
      //   this.generateMembersTotals();
      //   event.target.dispatchEvent(new Event('input', { bubbles: true }));  // trigger input event to change validation to new value
      //   return;
      // }else{
        bl.quantiteLivree = event.target.value;
        // event.target.dispatchEvent(new Event('input', { bubbles: true }));  // trigger input event to change validation to new value
      // }
      // sum of bl livrees
      let lineMax = 0;
      this.enteteBlUnitaire.forEach(bl => bl.lignes.forEach(ligne => {
        if(ligne.blocOffreId === blocOffreId){

            lineMax += Number(ligne.quantiteLivree);

    }}));


    const blConsolideTarget = this.enteteBlConsolide.lignes.find(bl => bl.blocOffreId === blocOffreId);
    if(lineMax > blConsolideTarget.quantiteLivree){
        blConsolideTarget['isvalidated'] = false;
        blConsolideTarget['raison'] = 'La somme des quantités livrées ne peut pas être supérieure à la somme des quantités Dans le bloc';
      }else{
        blConsolideTarget['isvalidated'] = undefined;
        blConsolideTarget['raison'] = undefined;
        bl.quantiteLivree = event.target.value;
      }

      if(this.validateSumOfRepartirisTheSameAsLivreePerLigne(false)){
        blConsolideTarget['isvalidated'] = undefined;
        blConsolideTarget['raison'] = undefined;
        bl.quantiteLivree = event.target.value;
      }else{
        blConsolideTarget['isvalidated'] = false;
        blConsolideTarget['raison'] = 'La somme des quantités livrées ne peut pas être inférieure à la quantité livrée de produit';
      }
    }
    if(field === 'quantiteUg'){
      if(bl.quantiteUg > bl.quantiteUgLivree){
        event.target.value = 0;
        bl.quantiteUg = 0;
        this.alertService.error("La quantité UG ne peut pas être supérieure à la quantité UG commandée", 'MODAL')
      }else{
        bl.quantiteUg = event.target.value;
      }
      //sum of bl livrees
      let lineMax = 0;
      this.enteteBlUnitaire.forEach(bl => bl.lignes.forEach(ligne => {
        if(ligne.blocOffreId === blocOffreId){
            lineMax += +ligne.quantiteUg;
        }
      }));
      const blConsolideTarget = this.enteteBlConsolide.lignes.find(bl => bl.blocOffreId === blocOffreId);

      if(lineMax > blConsolideTarget.quantiteUg){
        event.target.value = 0;
        bl.quantiteUg = 0;
        this.alertService.error("La somme des quantités UG livrées ne peut pas être supérieure à la somme des quantités UG de produit " + blConsolideTarget.designation, 'MODAL')
      }
      if(lineMax < blConsolideTarget.quantiteUg){
        blConsolideTarget['isvalidated'] = false;
        blConsolideTarget['raison'] = 'La somme des quantités livrées ne peut pas être inférieure à la quantité livrée de produit';
      }else{
        blConsolideTarget['isvalidated'] = undefined;
        blConsolideTarget['raison'] = undefined;
        bl.quantiteUg = event.target.value;
      }

      if(event.target.value === ''){
        bl.quantiteUg = 0;
        event.target.value = 0;
      }
    }
    bl['changed'] = true;
    bl['changeField'] = field;
    this.generateMembersTotals();

  }


  rowClass(row:RowClassArgs){
    if(row.dataItem && row.dataItem.isvalidated === false){
      return 'line-invalid';
    }
    return 'line-valid';
  }




    sendBLSUnitaire() {
      this.userInputService.confirmAlt('Confirmation', `Êtes-vous sûr de vouloir envoyer le bon de sortie ?`).then(async (confirmed) => {
        if (confirmed && this.valiateAllUsersHaveEmail()) {
          this.fsBLService.envoyerBlsUnitaire({blConsolideId: this.idBL}).subscribe(res => {
            this.alertService.successAlt(`Le bon de sortie a été envoyé avec succès!`, 'Envoyer Bon de Sortie', 'MODAL');
           });
        }
      }, () => null);
    }

    l(){
      console.log("l")
    }

    valiateAllUsersHaveEmail(){
      const usersHaventEmail = []
      for (const bl of this.enteteBlUnitaire) {
        if(!bl.enteteCommandeAchatGroupe.client.email){
          usersHaventEmail.push(bl.enteteCommandeAchatGroupe.client.nomResponsable)
          this.membersHasNoEmail.push(

            {
              nom: bl.enteteCommandeAchatGroupe.client.nomResponsable,
              raisonSociale: bl.enteteCommandeAchatGroupe.client.raisonSociale,
              email: bl.enteteCommandeAchatGroupe.client.email,
            }
          )
        }
      }
      let finalMessage = ''
      if(usersHaventEmail.length > 5){
        finalMessage = `${usersHaventEmail.slice(0,5).join(', ')} <br> <span  class="pointer-cus show-more-members  d-flex align-items-center justify-content-center k-gap-1 mt-2">et ${usersHaventEmail.length - 5} autres...<i class="bi bi-chevron-right" style="line-height: 1;"></i></span>`
        setTimeout(() => {
          this.listenForShowMoreMembers()
        }, 100)
      }
      else{
        finalMessage = usersHaventEmail.join(', ')
      }

      if(usersHaventEmail.length){
        this.alertService.error(`Vous devez renseigner l'email des membres <br>  ${finalMessage} <br> avant de envoyer le bons de sorties`, 'MODAL')
        return false
      }
      return true
    }


    listenForShowMoreMembers(){
      const showMoreMembers = document.querySelector('.show-more-members') as HTMLElement;
      if(showMoreMembers){
        showMoreMembers.addEventListener('click', e => {
          e.preventDefault()
       this.modalService.open(this.ModalShowMoreMembers,{
        size: 'lg'
       })

        })
      }
    }







    back() {
        this.router.navigate(['/achats-groupes/bons-livraison/liste']);
    }

    showmodalByrouterQueryParam(){
      this.router.navigate([], {
        queryParams: {
          action: 'bls-envoyes'
        }
      })
      this.showModalForDispatch()
    }

    showModalForDispatch(modal=this.ChoseBLUnitaire,size='xl'){
        if(modal){
          this.modalService.open(modal, { size, windowClass: 'fs-cstm-modal' ,modalDialogClass:'large-modal' });
        }

    }

    validateSumOfRepartirisTheSameAsLivreePerLigne(shouldAlert = true) {
     for (const Consolideligne of this.enteteBlConsolide.lignes) {
      let lineMax = 0;
      for (const bl of this.enteteBlUnitaire) {
        for (const ligne of bl.lignes) {
          if (ligne.blocOffreId === Consolideligne.blocOffreId) {
            lineMax += +ligne.quantiteLivree;
          }
        }
      }
      if (lineMax < Consolideligne.quantiteLivree) {
        shouldAlert && this.alertService.error(`La somme des quantités livrées ne peut pas être inférieure à la quantité livrée de produit ${Consolideligne.designation}`, 'MODAL')
        return false;
      }else if(lineMax > Consolideligne.quantiteLivree){
        shouldAlert && this.alertService.error(`La somme des quantités livrées ne peut pas être supérieure à la quantité livrée de produit ${Consolideligne.designation}`, 'MODAL')
        return false;
      }
     }
     return true;
    }






    generateTotals(){
      if (this.isCoffretEnabled) {
        return  aggregateBy(
          this.gridData.data,
          [
            {field:'qteFixePrdInCoffret',aggregate:'sum'},
            {field:'quantiteLivree',aggregate:'sum'},
            {field:'quantiteUg',aggregate:'sum'},
            {field:'quantiteCommandee',aggregate:'sum'}
          ]
        );
      }
      return  aggregateBy(
        this.gridData.data,
        [
          {field:'quantiteLivree',aggregate:'sum'},
          {field:'quantiteUg',aggregate:'sum'},
          {field:'quantiteCommandee',aggregate:'sum'}
        ]
      );
    }

    generateMembersTotals() {
      this.memeberSummary = new Map();
      this.enteteBlUnitaire.forEach(bl => {
        bl.lignes.forEach(ligne => {
          const clientId = bl.enteteCommandeAchatGroupe.client.id;
          const existingSummary = this.memeberSummary.get(clientId) || {
            quantiteLivree: 0,
            quantiteUg: 0,
            quantiteCommandee: 0,
          };

          // Sum the current values with the existing ones
          const updatedSummary = {
            quantiteLivree: +existingSummary.quantiteLivree + +ligne.quantiteLivree,
            quantiteUg: +existingSummary.quantiteUg + +ligne.quantiteUg,
            quantiteCommandee: +existingSummary.quantiteCommandee + +ligne.quantiteCommandee,
          };

          // Update the map with the new summed values
          this.memeberSummary.set(clientId, updatedSummary);
        });

        if (this.isCoffretEnabled) {
          const clientId = bl.enteteCommandeAchatGroupe.client.id;
          const existingSummary = this.memeberSummary.get(clientId);

          existingSummary['coffretCommandee'] = existingSummary.quantiteCommandee / this.generateTotals()['qteFixePrdInCoffret']?.sum;

          this.memeberSummary.set(clientId, existingSummary);
        }

      });
    }




    markAllAsUnChanged(){
      this.enteteBlUnitaire.forEach(bl => {
        bl.lignes.forEach(ligne => {
         delete ligne['changed'];
         delete ligne['changeField'];
        })
      });
    }

    saveBl() {
      if(!this.validateSumOfRepartirisTheSameAsLivreePerLigne()){
        return;
      }
      const details :DetailBlAchatGroupeDTO[] = []
      this.enteteBlUnitaire.forEach(bl => {
        bl.lignes.forEach(ligne => {
          details.push({
            ...ligne, enteteBl: {id: bl.id} as EnteteBlConsolideeMarcheDTO,
          })
        })

      });

      details.forEach(detail => {
        detail.montant = ( detail.pphRemise ?? detail.pph) * detail.quantiteLivree
      })

      const finalDetails =  details.filter(detail => detail['changed'] === true)

      if(finalDetails.length === 0){
        this.alertService.error("Aucune ligne n'a été modifiée", 'MODAL')
        return;
      }

      this.fsBLService.saveBlDispatcher(finalDetails).subscribe(_ => {
        this.markAllAsUnChanged()
        this.alertService.successAlt(`Le Dispatch de BL-${this.enteteBlConsolide.numeroBl} a été enregistré avec succès!`, 'Enregistrement de Dispatch', 'MODAL');
      });





    }


    getBlockOfferByBlocId(id:number,blockId:number){
     let bl = null ;
     this.enteteBlUnitaire.map(bls => {
       bl = bls.lignes.find(ligne => ligne?.blocOffreId === blockId && ligne?.id === id)
     });
     return bl;
     }

    validateDispatch() {
      if(!this.validateSumOfRepartirisTheSameAsLivreePerLigne()){
        return;
      }

     this.userInputService.confirmAlt('Confirmation', `Voulez-vous valider le  Dispatch de BL-${this.enteteBlConsolide.numeroBl} ?`).then((confirmed) => {
        if (confirmed) {

          const details:DetailBlAchatGroupeDTO[] = []
            this.enteteBlUnitaire.forEach(bl => {
              bl.lignes.forEach(ligne => {
                details.push({
                  ...ligne, enteteBl: bl,
                })
              })

            });

            details.forEach(detail => {
              detail.montant = ( detail.pphRemise ?? detail.pph) * detail.quantiteLivree
            })
            const finalDetails =  details.filter(detail => detail['changed'] === true)
            this.fsBLService.saveBlDispatcher(finalDetails).subscribe(res => {
            this.fsBLService.validateBlDispatcher({blConsolideId: this.idBL}).subscribe(res => {
            this.reInit();
            this.markAllAsUnChanged()
            this.alertService.successAlt(`Le Dispatch de BL-${this.enteteBlConsolide.numeroBl} a été validé avec succès!`, 'Validation de Dispatch', 'MODAL');
          });

        });
        }
      }).catch(()=>{});
    }

    pushMobileMenuOptions() {
      this.deferredActionBtnService.pushPageOptions([
        {
          iconClass: 'bi bi-bookmark-check-fill',
          label: 'Enregistrer',
          shouldShow: !this.isAllValidated,
          action: () => this.saveBl()
        },
        {
          iconClass: 'bi bi-check',
          label: 'Valider',
          shouldShow: !this.isAllValidated,
          action: () => this.validateDispatch()
        },
        {
          iconClass: 'bi bi-file-earmark-text',
          label: 'Bons de sortie',
          shouldShow: this.isAllValidated && this.enteteBlConsolide?.etatBl === 'REPARTI',
          action: () => this.showmodalByrouterQueryParam()
        },
        {
          iconClass: 'mdi mdi-close',
          label: 'Quitter',
          shouldShow: true,
          action: () => this.back()
        }
      ]);
    }


    ngOnDestroy(): void {
      this.deferredActionBtnService.pushPageOptions([]);
    }

  async  deleteDispatch() {
         const groupe =  await this.fsService.getMyGroupe()
      const groupeId = groupe.id;
      this.userInputService.confirmAlt('Confirmation', `Voulez-vous supprimer le  Dispatch de BL-${this.enteteBlConsolide.numeroBl} ?`).then((confirmed) => {
        if (confirmed) {
          this.fsBLService.deleteDispatch({blConsolideId: this.idBL,groupeId}).subscribe(res => {
            this.back();
            this.alertService.successAlt(`Le Dispatch de BL-${this.enteteBlConsolide.numeroBl} a été supprimé avec succès!`, 'Suppression de Dispatch', 'MODAL');
          });
        }
      });
    }
}

