.input-container {
  position: relative;
  margin-bottom: 8px;
  input {
    background: #F8F8F8;
    border: 1px solid #707070;
    border-radius: 23px;
    padding: 7px;
    opacity: 1;
    width: 100%;
    border-color: #3DA5D9;
    padding-left:17px;
    margin-top:6px;
  }
  .title {
    margin-left:15px;
    font-size:14px !important;
  }
  .error-messages {
    color: #FF0000;
    position: absolute;
    top: 68px;
    right: 20px;
    font-size: 12px;
    margin-left:15px;

  }

  input::-ms-reveal,
  input::-ms-clear {
    display: none;
  }

  .required {
    color: #FF0000;
  }

  .error {
    border-color: #FF0000;
  }

  .error:focus {
      border-color: #FF0000;
  }
}
.input-group-text {
   display: flex;
   justify-content: end;
   align-items: center;
  position: absolute !important;
  top: 40px !important;
  right: 13px !important;
  ion-icon{
    font-size: 22px !important;
    color: #3da5d9;
  }
}
