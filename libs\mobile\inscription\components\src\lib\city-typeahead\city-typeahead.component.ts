import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  forwardRef,
} from '@angular/core';
import { ControlContainer, ControlValueAccessor, FormControl, FormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { InscriptionService } from '@wph/mobile/shared';
import { City } from '@wph/shared';
import { Subject, debounceTime, distinctUntilChanged, map, takeUntil, tap } from 'rxjs';

@Component({
  selector: 'wph-city-typeahead',
  templateUrl: './city-typeahead.component.html',
  styleUrls: ['./city-typeahead.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CityTypeaheadComponent),
      multi: true,
    },
  ],
})
export class CityTypeaheadComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() selectedCityControlName: string;

  cities: City[] | null = null;
  selectedCity: City | null = null;
  filteredCities: City[] = [];

  parentForm: FormGroup;
  control: FormControl;

  isLoading: boolean = false;
  searchQuery$: Subject<string> = new Subject<string>();
  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  onChange = (_value: City) => { };

  constructor(
    private modalController: ModalController,
    private controlContainer: ControlContainer,
    private inscriptionService: InscriptionService
  ) { }

  ngOnInit(): void {
    this.parentForm = this.controlContainer.control as FormGroup;
    this.control = this.parentForm.controls[this.selectedCityControlName] as FormControl;

    this.getCities();

    this.searchQuery$.pipe(
      takeUntil(this.unsubscribe$),
      tap(() => this.isLoading = true),
      debounceTime(400),
      distinctUntilChanged(),
      map(query => {
        this.filterList(query);
      })
    ).subscribe(() => this.isLoading = false);
  }

  getCities(): void {
    this.inscriptionService.getCities().subscribe((cities) => {
      this.filteredCities = this.cities = cities;
    });
  }

  writeValue(value: City): void {
    this.selectedCity = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void { }

  trackItems(index: number, item: City) {
    return item.labelFr;
  }

  cancelChanges() {
    this.modalController.dismiss();
  }

  confirmChanges() {
    this.onChange(this.selectedCity);
    this.control.setValue(this.selectedCity?.labelFr);

    this.modalController.dismiss();
  }

  searchbarInput(ev: any) {
    this.searchQuery$.next(ev?.detail?.value);
  }

  filterList(searchQuery: string) {
    if (searchQuery === '') {
      this.filteredCities = [...this.cities];
    } else {
      const normalizedQuery = searchQuery.toLowerCase();
      this.filteredCities = this.cities.filter((city) => {
        return city.labelFr.toLowerCase().includes(normalizedQuery);
      });
    }
  }

  radioChange(ev: any) {
    this.selectedCity = ev?.detail?.value;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }
}
