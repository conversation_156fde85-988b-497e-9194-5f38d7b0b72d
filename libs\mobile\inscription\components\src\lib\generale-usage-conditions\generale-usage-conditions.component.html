<div class="container">
  <!--  <ion-segment value="new" (ionChange)="handleChange($event)">-->
  <!--    <ion-segment-button value="new">-->
  <!--      <ion-label>Nouveau</ion-label>-->
  <!--    </ion-segment-button>-->
  <!--    <ion-segment-button value="code">-->
  <!--      <ion-label>Code</ion-label>-->
  <!--    </ion-segment-button>-->
  <!--  </ion-segment>-->
  <p class="custom-page-title-terms" >Conditions générales d'utilisation</p>


  <p class="terms" #terms (scroll)="onScroll($event)">
    En acceptant donc nos conditions générales vous consentez à l’utilisation de nos systèmes tels que décrits ci-après. Toutefois, si vous ne souhaitez pas participer à un programme de fidélité, vous pouvez à tout moment vous connecter à votre application et vous désinscrire ou désinscrire un membre de votre équipe. Si vous n’y parvenez pas, n’hésitez pas à nous contacter sur.
    En acceptant donc nos conditions générales vous consentez à l’utilisation de nos systèmes tels que décrits ci-après. Toutefois, si vous ne souhaitez pas participer à un programme de fidélité, vous pouvez à tout moment vous connecter à votre application et vous désinscrire ou désinscrire un membre de votre équipe. Si vous n’y parvenez pas, n’hésitez pas à nous contacter sur.
    En acceptant donc nos conditions générales vous consentez à l’utilisation de nos systèmes tels que décrits ci-après. Toutefois, si vous ne souhaitez pas participer à un programme de fidélité, vous pouvez à tout moment vous connecter à votre application et vous désinscrire ou désinscrire un membre de votre équipe. Si vous n’y parvenez pas, n’hésitez pas à nous contacter sur.
    En acceptant donc nos conditions générales vous consentez à l’utilisation de nos systèmes tels que décrits ci-après. Toutefois, si vous ne souhaitez pas participer à un programme de fidélité, vous pouvez à tout moment vous connecter à votre application et vous désinscrire ou désinscrire un membre de votre équipe. Si vous n’y parvenez pas, n’hésitez pas à nous contacter sur
  </p>

  <div class="send-button-container">
    <div><wph-button class="send-button" [disabled]="!isScrolled" (click)="accept()">Accepter les conditions</wph-button></div>
    <div><wph-button class="send-button" (click)="handleScrollClick()" [disabled]="true"><img src="assets/images/arrow-down.png" /></wph-button></div>
  </div>
</div>
