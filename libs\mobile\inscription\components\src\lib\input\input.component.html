<div class="input-container">
  <span class="title" *ngIf="title"><b>{{title}} <span class="required" *ngIf="required">*</span></b></span>
    <input
    [type]="isPassword ? type : 'text'"
    [formControl]="control"
    [value]="value"
    [readOnly]="readonly"
    [placeholder]="placeholder"
    (change)="updateValue($any($event.target)?.value)"
    [ngClass]="{'custom-style': true, 'error': control?.invalid && control?.touched}"
  />
    <div class="input-group-text" (click)="isPassword = !isPassword" *ngIf="type === 'password'" >
      <ion-icon name="eye-outline" *ngIf="isPassword"></ion-icon>
      <ion-icon name="eye-off-outline" *ngIf="!isPassword"></ion-icon>
    </div>

<!--  <input-->
<!--    *ngIf="type === 'number'"-->
<!--    [formControl]="control"-->
<!--    [value]="value"-->
<!--    type="number"-->
<!--    (change)="updateValue($any($event.target)?.value)"-->
<!--    [ngClass]="{'custom-style': true, 'error': control?.invalid && control?.touched}"-->
<!--  />-->

  <div *ngIf="control?.invalid && control.touched" class="error-messages">
    <p *ngIf="control?.errors['required']">Ce champ est Obligatoire.</p>
    <p *ngIf="control?.errors['pattern']">Le numero est incorrect</p>
    <p *ngIf="control?.errors['email']">format email incorrect</p>
    <!-- Add other error messages as needed -->
  </div>

</div>


