<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/commandes"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Mes ' + statutTranslation}}</ion-title>

    <ion-buttons slot="end">
      <ion-button id="options-icon">
        <ion-icon name="filter" size="small"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">

  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-grid>
    <ion-row>
      <ion-col class="ion-no-padding">
        <ion-list class="ion-no-margin" lines="none">
          <ion-item-divider sticky class="sticky-item-list">

            <ion-searchbar [value]="searchValue" placeholder="Rechercher par code ou titre" show-clear-button="focus"
              (ionInput)="onTextChangeFunc($event)" #searchbar></ion-searchbar>

            <ion-item class="selected-date-banner"
              *ngIf="isFilterApplied && (filterControls['dateDebut']?.dirty || filterControls['dateFin']?.dirty)">
              <ion-item>
                <ion-label>Commandes du : </ion-label>
                <ion-badge class="selected-segment-badge-alt" (click)="isFilterModalOpen = true">
                  <span *ngIf="filterControls['dateDebut']?.dirty; else: dateNotFilled"> {{
                    filterControls['dateDebut']?.value | date : 'dd/MM/y' }} </span>
                </ion-badge>
              </ion-item>

              <ion-item>
                <ion-label>au : </ion-label>
                <ion-badge class="selected-segment-badge-alt" (click)="isFilterModalOpen = true">
                  <span *ngIf="filterControls['dateFin']?.dirty; else: dateNotFilled"> {{
                    filterControls['dateFin']?.value | date : 'dd/MM/y' }} </span>
                </ion-badge>
              </ion-item>

              <ng-template #dateNotFilled>
                <span>jj/mm/aaaa</span>
              </ng-template>

            </ion-item>

          </ion-item-divider>

          <wph-commande-item *ngFor="let item of data; trackBy: trackItems" [item]="item"
            [societe]="societe" (click)="handlepress(item)"></wph-commande-item>

          <div class="first-load-container" *ngIf="firstLoad || loading">
            <ion-spinner></ion-spinner>
          </div>

        </ion-list>

        <ion-infinite-scroll (ionInfinite)="doInfinite($event)" [disabled]="page > totalPages">
          <ion-infinite-scroll-content *ngIf="!firstLoad"></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-col>
    </ion-row>
  </ion-grid>

  <wph-empty-list *ngIf="!loading && !error && data && data.length === 0"></wph-empty-list>
  <wph-error *ngIf="!firstLoad && error"></wph-error>
</ion-content>

<wph-command-filters trigger="options-icon" [isOpen]="isFilterModalOpen" [formGroup]="commandeFilterForm"
  (filterApplied)="applyOrClearFilters($event)" (dismissed)="isFilterModalOpen = false"
  [showCommandStatusOptions]="!statut.includes('B')">
</wph-command-filters>