import { Component, Input } from '@angular/core';
import { Commande, Offre, OffresService } from "@wph/data-access";
import {<PERSON><PERSON><PERSON>ontroller, LoadingController, ModalController, NavController} from "@ionic/angular";
import {StateModalComponent} from "../../../../../shared/src/lib/state-modal/state-modal.component";
import { ClientFournisseur } from '@wph/shared';

@Component({
  selector: 'wph-confirmation-commande',
  templateUrl: './confirmation-commande.page.html',
  styleUrls: ['./confirmation-commande.page.scss'],
})
export class ConfirmationCommandePage {
  @Input() selectedOffre: Offre;
  @Input() savedCommande: Commande;
  @Input() selectedClientLocal: ClientFournisseur | string;
  message: string = '';

  constructor(
    private navController: NavController, 
    private alertController: AlertController, 
    private offresService: OffresService,
    private modalController: <PERSON><PERSON><PERSON><PERSON>roll<PERSON>, 
    private loadingCtrl: Loading<PERSON><PERSON>roller
  ) {}

  dismissModal() {
    this.modalController.dismiss();
  }

  async valider() {
    const alert = await this.alertController.create({
      header: 'Message',
      message:'Confirmez votre commande ?',
      cssClass:'confirm-cmd',
      buttons: [
        {
          text: 'Annuler',
          role: 'cancel',
          handler: () => {

          },
        },
        {
          text: 'Confirmer',
          role: 'confirm',
          handler: async () => {
            const loading = await this.loadingCtrl.create({
              message: 'Enregistement...',
              duration: 3000
            });

            (this.selectedOffre as any).commentaire = this.message;

            this.offresService.createCommande(this.selectedOffre).subscribe(data => {


              loading.present();
              this.savedCommande = data;
              loading.dismiss();
              this.offresService.valdierCommandeById(this.savedCommande.id).subscribe(() => {
                this.presentMessageModal('La commande a été bien Commandée.', 'success').then((success) => {
                });
                this.dismissModal();
                this.navController.navigateRoot('/commandes/tr', { replaceUrl: true });
              });

            }, (err) => {
              loading.dismiss();
              this.presentMessageModal(err.error.message ? err.error.message : 'Erreur, Merci de ressayer!', 'error');
            });

          },
        },
      ],
    });

    await alert.present();

    const { role } = await alert.onDidDismiss();

  }

  async presentMessageModal(message: string, type: string) {
    const alert = await this.modalController.create({
      cssClass: 'small-modal',
      component: StateModalComponent,
      componentProps: {
        message: message,
        type: type
      }
    });

    await alert.present();
  }
}
