import { Component, OnInit } from '@angular/core';
import { Pagination } from '@wph/data-access';
import { AuthLogService } from '../../../services/auth-log.service';
import { AuthLogCriteria } from '../../../models/auth-log.model';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { PageChangeEvent } from '@progress/kendo-angular-pager';
import { SortDescriptor } from '@progress/kendo-data-query';
import { FormControl, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'wph-auth-log',
  templateUrl: './auth-log.component.html',
  styleUrls: ['./auth-log.component.scss']
})
export class AuthLogComponent implements OnInit {

  navigation: Pagination = { pageSize: 15, skip: 0, sortField: 'logDate', sortMethod: 'desc' };

  authLogCriteria: AuthLogCriteria;

  unsubscribe$: Subject<boolean> = new Subject<boolean>();

  groupeSort: SortDescriptor[];

  displayFilter = false;

  authLogForm: FormGroup;


  codeClientSeach : FormControl;


  gridData: GridDataResult = { total: 0, data: [] };

  constructor(private authLogService:AuthLogService) { }

  pageSizes = [5, 10, 15, 20];

  ngOnInit() {
    this.initFilterForm()
    this.authLogCriteria = new AuthLogCriteria({});
    this.searchAuthLog();
    this.listenToSearchFilterChanges();
  }

  OnPageChange(event: any): void {
    this.searchAuthLog();
  }


  clearFilterCommandes() {
    this.authLogForm.reset();
    this.codeClientSeach.reset();
    this.authLogForm.get('dateDebut').setValue(null);
    this.authLogForm.get('dateFin').setValue(null);
    this.navigation.skip =0;
    this.searchAuthLog();

  }



  initFilterForm() {
    this.authLogForm = new FormGroup({
      adresseIp: new FormControl(null),
      dateDebut: new FormControl(null),
      dateFin: new FormControl(null),
      nomPrenomOperateur: new FormControl(null),
    });
    this.codeClientSeach = new FormControl(null)
}


listenToSearchFilterChanges(): void {
  this.codeClientSeach.valueChanges
    .pipe(
      takeUntil(this.unsubscribe$),
      debounceTime(200),
      distinctUntilChanged()
    )
    .subscribe(() => {
      this.navigation.skip =0;
     this.searchAuthLog();
    });
}



  searchAuthLog() {
   this.authLogService.searchAuthLog(this.BuildFilterModel(), this.navigation).subscribe(res => {
      this.gridData.data = res.content;
      this.gridData.total = res.totalElements;
    });
  }

  BuildFilterModel() {
    const authLogCriteria = new AuthLogCriteria({
      adresseIp: this.authLogForm.get('adresseIp').value,
      dateDebut: this.authLogForm.get('dateDebut').value,
      dateFin: this.authLogForm.get('dateFin').value,
      nomPrenomOperateur: this.authLogForm.get('nomPrenomOperateur').value,
    });
    if(this.codeClientSeach.value){
      authLogCriteria.operateur ={};
      authLogCriteria.operateur.entrepriseDTO = {
        code :""
      }
      authLogCriteria.operateur['entrepriseDTO']['code'] = this.codeClientSeach.value
    }
    return authLogCriteria;
  }





  sortChange(sort: SortDescriptor[]): void {
    this.groupeSort = sort;

    if (this.groupeSort && this.groupeSort.length > 0 && this.groupeSort[0].dir) {
        this.navigation.sortField = sort[0].field;
        this.navigation.sortMethod = sort[0].dir;
    } else {
        this.navigation.sortField = null;
        this.navigation.sortMethod = null;
    }

      this.searchAuthLog();

}

pageChange(event: PageChangeEvent): void {
    if ((event.skip !== this.navigation.skip) || (event.take !== this.navigation.pageSize)) {
        this.navigation.skip = event.skip;
        this.navigation.pageSize = event.take;
    }
}

}
