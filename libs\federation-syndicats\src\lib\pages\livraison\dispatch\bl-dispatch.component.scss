.btn-success{
    background-color: var(--fs-success) !important;
    border-color: var(--fs-success) !important;
}



.text-success{
  color: var(--fs-success) !important;
}



.btn-exapnd-details{
    background-color: var(--wf-primary-400) !important;
    border-color: var(--wf-primary-400) !important;
    height: 25px;
    width: 25px;
    padding: 0;
    font-size: 1rem;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    position: absolute;
    cursor: pointer;
    right: 0;
    bottom: 0;
i{
    line-height: 1;
}
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
  }

  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }


.divider-y > *:not(:last-child) {
    border-right: 1px solid #d5d5d5 !important;
  }

.btn-danger{
    background-color: var(--fs-danger) !important;
    border-color: var(--fs-danger) !important;
}



.tabs-separate{
  background: #f0f2f5;
  width: 20px;
  /* height: 100%; */
  flex: none !important;
  @media screen and (max-width: 768px) {
    display: none;

  }
}



::ng-deep .bl-header-actions app-export-pdf button[title="Imprimer"]{
  margin: 0 !important;
  padding-inline: 8px !important;
  padding-block: 2px !important;
  i{
    font-size: 24px;
  }
}


::ng-deep .bl-grid .k-grid-content .line-invalid ,
::ng-deep .bl-grid .k-grid-content .line-invalid:hover{
  background-color: rgba(253, 47, 47, 0.301) !important;
  }


  ::ng-deep .bl-grid .k-grid-content .line-valid ,
::ng-deep .bl-grid .k-grid-content .line-valid:hover{
    background-color: rgba(0, 255, 0, 0.103) !important;
  }



.bl-product-container {
  display: flex;
  align-items: center;
  position: relative;
  gap: 10px;

}

.bl-product-alert-icon {
  background-color: var(--fs-warning);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bl-product-card {
  display: flex;
  align-items: stretch;
  color: #000;
  border-radius: 5px;
  padding: 3px;

  &.ok{
    background: var(--fs-success);
    color: #f0f2f5;

  }
  &.warn{
    background: var(--fs-warning);
    color: #f0f2f5;
  }
}

.bl-product-card-quantity {

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  background: #fff;
  color: var(--fs-warning);
  padding-inline: 6px;
}

.bl-product-card-text {
  font-size: 16px;
  padding-inline: 10px;
  padding-block: 5px;
  line-height: 1;
}



::ng-deep .large-modal{
  width: 80% !important;
  max-width: inherit !important;
  @media screen and (max-width: 768px) {
    width: 100% !important;
  }
}


input[readonly] {
  color: black !important;
  font-weight: 600 !important;;
}
