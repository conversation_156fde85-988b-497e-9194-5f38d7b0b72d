.btn-success{
    background-color: var(--fs-success) !important;
    border-color: var(--fs-success) !important;
}



.text-success{
  color: var(--fs-success) !important;
}



.btn-exapnd-details{
    background-color: var(--wf-primary-400) !important;
    border-color: var(--wf-primary-400) !important;
    height: 25px;
    width: 25px;
    padding: 0;
    font-size: 1rem;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 5px;
    position: absolute;
    cursor: pointer;
    right: 0;
    bottom: 0;
i{
    line-height: 1;
}
}

.b-radius {
    border-radius: var(--winoffre-base-border-radius) !important;
  }

  .picker-input {
    .form-control {
      border-radius: var(--winoffre-base-border-radius) !important;
    }
  }


.divider-y > *:not(:last-child) {
    border-right: 1px solid #d5d5d5 !important;
  }

.btn-danger{
    background-color: var(--fs-danger) !important;
    border-color: var(--fs-danger) !important;
}



.tabs-separate{
  background: #f0f2f5;
  width: 20px;
  /* height: 100%; */
  flex: none !important;
  @media screen and (max-width: 768px) {
    display: none;

  }
}



::ng-deep .bl-header-actions app-export-pdf button[title="Imprimer"]{
  margin: 0 !important;
  padding-inline: 8px !important;
  padding-block: 2px !important;
  i{
    font-size: 24px;
  }
}


::ng-deep .bl-grid .k-grid-content .line-invalid ,
::ng-deep .bl-grid .k-grid-content .line-invalid:hover{
  background-color: rgba(253, 47, 47, 0.301) !important;
  }


  ::ng-deep .bl-grid .k-grid-content .line-valid ,
::ng-deep .bl-grid .k-grid-content .line-valid:hover{
    background-color: rgba(0, 255, 0, 0.103) !important;
  }



.bl-product-container {
  display: flex;
  align-items: center;
  position: relative;
  gap: 10px;

}

.bl-product-alert-icon {
  background-color: var(--fs-warning);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bl-product-card {
  display: flex;
  align-items: stretch;
  color: #000;
  border-radius: 5px;
  padding: 3px;

  &.ok{
    background: var(--fs-success);
    color: #f0f2f5;

  }
  &.warn{
    background: var(--fs-warning);
    color: #f0f2f5;
  }
}

.bl-product-card-quantity {

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  background: #fff;
  color: var(--fs-warning);
  padding-inline: 6px;
}

.bl-product-card-text {
  font-size: 16px;
  padding-inline: 10px;
  padding-block: 5px;
  line-height: 1;
}



::ng-deep .large-modal{
  width: 80% !important;
  max-width: inherit !important;
  @media screen and (max-width: 768px) {
    width: 100% !important;
  }
}


input[readonly] {
  color: black !important;
  font-weight: 600 !important;;
}

// Modern Exchange Modal Styles
.modal-icon {
  width: 48px;
  height: 48px;
  background: rgba(13, 110, 253, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-indicator {
  .progress {
    border-radius: 10px;
    background-color: rgba(13, 110, 253, 0.1);

    .progress-bar {
      border-radius: 10px;
      transition: width 0.3s ease;
    }
  }
}

.exchange-form {
  .member-selection-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;

    &:hover {
      border-color: #0d6efd;
      box-shadow: 0 2px 8px rgba(13, 110, 253, 0.1);
    }

    .card-header {
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 12px 16px;
      font-size: 14px;
      display: flex;
      align-items: center;
    }

    .card-body {
      padding: 16px;
    }

    .modern-select {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 14px;
      transition: all 0.2s ease;

      &:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }
    }

    .member-info {
      padding: 8px 12px;
      background: rgba(13, 110, 253, 0.05);
      border-radius: 6px;
      border-left: 3px solid #0d6efd;
    }
  }
}

.products-exchange-section {
  .section-header {
    padding: 16px 0;
    border-bottom: 1px solid #e9ecef;
  }

  .products-grid-container {
    .modern-table {
      margin-bottom: 0;

      thead th {
        background: #f8f9fa;
        font-weight: 600;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding: 16px 12px;
      }

      tbody {
        tr {
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(13, 110, 253, 0.02);
          }

          &.table-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
            border-left: 3px solid #ffc107;
          }

          &.table-danger {
            background-color: rgba(220, 53, 69, 0.1) !important;
            border-left: 3px solid #dc3545;
          }
        }

        td {
          padding: 16px 12px;
          vertical-align: middle;
          border-top: 1px solid #f1f3f4;
        }
      }
    }

    .product-info {
      .fw-medium {
        font-size: 14px;
        line-height: 1.4;
      }

      small {
        font-size: 12px;
        opacity: 0.7;
      }
    }

    .quantity-input-container {
      max-width: 140px;
      margin: 0 auto;

      .quantity-input {
        font-weight: 600;
        font-size: 14px;

        &.is-invalid {
          border-color: #dc3545;
        }
      }

      .btn {
        border-color: #dee2e6;

        &:hover:not(:disabled) {
          background-color: #0d6efd;
          border-color: #0d6efd;
          color: white;
        }

        &:disabled {
          opacity: 0.4;
        }
      }

      .invalid-feedback {
        font-size: 11px;
        margin-top: 4px;
      }
    }
  }
}

.pending-exchanges-section {
  .exchange-item {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .member-avatar {
    font-size: 12px;
  }
}

.modal-actions {
  .summary-stat {
    padding: 8px;

    .h5 {
      font-weight: 700;
    }

    small {
      font-size: 11px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Button improvements
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border: none;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
    }
  }

  &.btn-success {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%);
    border: none;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);
    }
  }

  &.btn-light {
    background: #f8f9fa;
    border: 1px solid #dee2e6;

    &:hover {
      background: #e9ecef;
      border-color: #adb5bd;
    }
  }
}

// Badge improvements
.badge {
  font-weight: 500;
  padding: 6px 12px;

  &.bg-light {
    color: #495057 !important;
  }
}
