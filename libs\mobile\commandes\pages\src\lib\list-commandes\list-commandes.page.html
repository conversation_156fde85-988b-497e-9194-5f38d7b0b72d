<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/"></ion-back-button>
    </ion-buttons>
    <ion-title>{{'Mon Historique'}}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper btn-cmd" (click)="openPage('commandes/br')">
         {{'Mes Brouillons'}}
        </ion-button>
      </ion-col>
      <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper btn-cmd" (click)="openPage('commandes/tr')">
          {{'Mes Commandes'}}
        </ion-button>
      </ion-col>
      <!-- <ion-col size="11">
        <ion-button shape="round" class="item-btn-wrapper btn-cmd" (click)="openPage('commandes/an')">
           <span>{{'Commandes Annulées'}}</span>
        </ion-button>
      </ion-col> -->
    </ion-row>
  </ion-grid>
</ion-content>
