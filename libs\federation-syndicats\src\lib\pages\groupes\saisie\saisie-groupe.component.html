<!-- Start Of Header -->
<div class="rowline mb-0">
    <div class="page-title-box row">
        <div class="d-flex align-items-center col-auto k-gap-4">
            <button class="actions-icons action-back btn text-white" (click)="back()">
                <i class="bi bi-chevron-left" style="font-size: 18px;"></i>
            </button>

            <h4 class="page-title fw-4 ps-2">{{ pageTitle }}</h4>
        </div>

        <div class="col-atuo px-0 mx-0">
            <div class="row justify-content-end align-items-center  responsive-actions">
                <ng-container *ngIf="!readOnly">
                    <button *jhiHasAnyAuthority="['ROLE_NATIONAL']" (click)="suggererPharmacie()" type="button"
                        class="btn btn-sm btn-primary m-1" style="padding-block: 6px;">
                        <i class="bi bi-shop-window"></i>
                        Suggérer Pharmacie
                    </button>

                    <button (click)="enregistrerGroupe()"
                        [disabled]="groupeForm?.invalid || !f['responsablesGroupe'].value?.length || (!f['ville'].value && !f['localite'].value)"
                        type="button" class="btn btn-sm btn-success m-1" style="padding-block: 6px;" #btnEnreg>
                        <i class="mdi mdi-content-save"></i>
                        Enregistrer
                    </button>
                </ng-container>

                <button (click)="back()" type="button" style="padding-block: 6px;"
                    class="btn btn-sm btn-dark text-white m-1">
                    <i class="mdi mdi-close"></i>
                    Quitter
                </button>
            </div>
        </div>
    </div>
</div>
<!-- END HEADER -->

<div class="row d-flex m-0 p-2 main-card">
    <div class="card m-0 w-100 p-0 bg-white">
        <div class="card-header p-0">
            <div class="row m-0 p-0">
                <div class="col-12 col-md-8 col-xl-7 bg-card-header d-flex align-items-center"
                    [ngClass]="{'justify-content-between': !readOnly}">
                    <input #largeInput [formControl]="$any(f['raisonSociale'])" [readOnly]="mode !== 'S'" type="text"
                        placeholder="Saisir le nom du groupe*" autocomplete="off" maxlength="100" />

                    <i *ngIf="!readOnly && f['raisonSociale']?.value?.length" class="char-count text-white mx-2">{{
                        f['raisonSociale']?.value?.length || 0 }}/100</i>

                    <i *ngIf="mode === 'S'" (click)="selectAll()"
                        class="mdi mdi-pencil-box-outline text-white float-right pointer-cus"
                        style="font-size: 2rem;"></i>
                </div>

                <div class="col-12 py-2 py-md-0 col-md-4 col-xl-5 d-flex align-items-center">
                    <div class="row px-3 w-100 d-flex justify-content-end">
                        <span *ngIf="groupeId; else: noGroupeId" (click)="activerOuDesactiverGroupe()">
                            <kendo-switch [readonly]="true" onLabel="Actif" [formControl]="$any(f['statutEntreprise'])"
                                offLabel="Inactif" class="status-switch-align pointer-cus"></kendo-switch>
                        </span>

                        <ng-template #noGroupeId>
                            <span>
                                <kendo-switch [readonly]="true" onLabel="Actif"
                                    [formControl]="$any(f['statutEntreprise'])" offLabel="Inactif"
                                    class="status-switch-align pointer-cus"></kendo-switch>
                            </span>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body pt-4 pb-1 px-2">
            <div class="row flex-wrap">
                <div class="{{readOnly ? 'col-md-6' : 'col-xl-4 col-md-6'}} mt-md-0 col-12 mt-2">
                    <label for="ville" class="form-label">{{ (!readOnly ? 'Assigner la ville / localité du groupe' :
                        'Ville du groupe') | titlecase }}
                        <span *ngIf="!readOnly" class="text-danger">*</span></label>

                    <div class="input-group picker-input">
                        <input type="text" [readOnly]="readOnly" class="form-control form-control-md pl-4" id="ville"
                            [formControl]="$any(f['ville'])" placeholder="Ville" [ngbTypeahead]="searchVilleOuLocalite"
                            [inputFormatter]="villeFormatter" [resultTemplate]="searchVilleTemplate" />

                        <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>

                        <ng-template #searchVilleTemplate let-result="result">
                            <b>{{ result?.labelFr }}</b>
                        </ng-template>
                    </div>
                </div>

                <div class="{{readOnly ? 'col-md-6' : 'col-xl-4 col-md-6'}} mt-md-0 col-12 mt-2">
                    <label for="localite" class="form-label position-relative d-flex align-items-center">
                        <span [style.visibility]="!readOnly ? 'hidden' : 'visible'">
                            {{ 'localité du groupe' | titlecase}}
                        </span>

                        <span *ngIf="!readOnly && f['localite']?.value?.length"
                            style="position: absolute; right: 10px; bottom: -5px">
                            <i class="char-count">{{ f['localite']?.value?.length || 0 }}/255</i>
                        </span>

                    </label>

                    <div class="input-group">
                        <input type="text" placeholder="Localité" [readOnly]="readOnly" placeholder="localité"
                            [ngbTypeahead]="searchLocalite" [inputFormatter]="localiteFormatter"
                            [resultTemplate]="searchLocalTemplate" class="form-control form-control-md" id="localite"
                            [formControl]="$any(f['localite'])" />
                    </div>
                    <ng-template #searchLocalTemplate let-result="result">
                        <b>{{ result?.localite }}</b>
                    </ng-template>
                </div>

                <div *ngIf="!readOnly" class="col-xl-4 col-md-6 mt-md-0 col-12 mt-2">
                    <label class="form-label mb-1" style="visibility: hidden;">Responsable</label>
                    <div class="input-group">
                        <button (click)="openModal(selectionnerChef)"
                            class="btn bt-sm bg-white text-dark w-100 shadow-sm">
                            <i class="bi bi-sliders"></i>
                            <span class="mx-1">Sélectionner le responsable du groupe</span>
                        </button>
                    </div>
                </div>

                <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                    <div *ngIf="!readOnly" class="col-xl-4 col-md-6 mt-md-0 col-12 mt-2">
                        <label class="form-label mb-1" style="visibility: hidden;">Responsable</label>
                        <div class="input-group py-0">
                            <button (click)="openModal(selectionnerChef, false)"
                                class="btn bt-sm bg-white b-text text-dark w-100 shadow-sm">
                                <i class="bi bi-sliders"></i>
                                <span class="mx-1">Sélectionner membres</span>
                            </button>
                        </div>
                    </div>
                </ng-container>
            </div>

            <div class="row mt-4">
                <div class="card w-100">


                    <div class="card-body p-0">
                        <kendo-grid id="fs-groupe-grid" [data]="gridData" class="fs-grid fs-grid-white" [sortable]="{ mode: 'single'}"
                            (selectionChange)="selectionChange($event)" [selectable]="gridSelectable"
                            (cellClick)="onCellClick($event)" [selectedKeys]="selectedKeys" kendoGridSelectBy="id"
                            #grid>
                            <ng-template kendoGridToolbarTemplate>
                                <div class="d-flex row justify-content-between align-items-center w-100 mx-0">
                                    <span class="h4 text-white">Membres du groupe</span>

                                    <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                                        <button *ngIf="selectedKeys?.length"
                                            (click)="openModalIdentifiants(modeEnvoieModal)" type="button"
                                            style="font-size: 1rem; font-weight: 600; border-radius: 10px"
                                            class="btn btn-md text-light bg-primary mx-1" title="Envoyer Identifiants">
                                            <i class="bi bi-envelope"></i>
                                            Envoyer Identifiants
                                        </button>
                                    </ng-container>
                                </div>
                            </ng-template>

                            <!-- select box -->
                            <kendo-grid-checkbox-column *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']" class="no-ellipsis" headerClass="no-ellipsis" [hidden]="!readOnly"
                                [width]="50" [showSelectAll]="true">
                            </kendo-grid-checkbox-column>

                            <kendo-grid-column title="Code" [width]="85">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    {{ dataItem?.code }}
                                </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column title="Raison Sociale" class="text-wrap" [width]="180">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    PH. {{ dataItem?.raisonSociale }}
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column title="Pharmacien(ne)" class="text-wrap" [width]="150">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span *ngIf="dataItem?.nomResponsable; else: indisponible">Dr. {{
                                        dataItem?.nomResponsable }} </span>
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column title="Ville / Localité" class="text-wrap" [width]="150">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span *ngIf="dataItem?.ville; else: indisponible">{{ dataItem?.ville }}</span>

                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-column field="email" title="Email" [width]="180">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span *ngIf="dataItem?.email; else: indisponible">{{ dataItem?.email }}</span>
                                </ng-template>

                            </kendo-grid-column>


                            <kendo-grid-column field="gsm1" title="GSM" [width]="150">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span class="fw-bold" *ngIf="dataItem?.gsm1; else: indisponible">{{
                                        dataItem?.gsm1 }}</span>
                                </ng-template>

                            </kendo-grid-column>

                            <kendo-grid-column field="telephone" title="Téléphone" [width]="150">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span class="fw-bold" *ngIf="dataItem?.telephone; else: indisponible">{{
                                        dataItem?.telephone }}</span>
                                </ng-template>

                            </kendo-grid-column>

                            <kendo-grid-column field="whatsapp" title="WhatsApp" [width]="150">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span class="fw-bold" *ngIf="dataItem?.whatsapp; else: indisponible">{{
                                        dataItem?.whatsapp }}</span>
                                </ng-template>

                            </kendo-grid-column>



                            <kendo-grid-column field="dispo" title="Statut Membre" class="text-center" [width]="140">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span *ngIf="!dataItem?.statutMembreGroupe"
                                        class="badge badge-grey rounded-pill py-1 px-2">
                                        {{'Inactif' | uppercase}}
                                    </span>

                                    <span *ngIf="dataItem?.statutMembreGroupe"
                                        class="badge badge-success rounded-pill py-1 px-2">
                                        {{'Actif' | uppercase}}
                                    </span> </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="role" title="Role" [width]="155">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <span
                                        [class]="dataItem?.role === 'member' ? 'badge-member-groupe' : 'badge-chef-groupe'">{{
                                        dataItem?.role === 'member' ? 'Membre' : 'Responsable' }}</span>
                                </ng-template>

                            </kendo-grid-column>

                            <kendo-grid-column title="Action" [width]="90">
                                <ng-template kendoGridCellTemplate let-dataItem>
                                    <div class="d-flex justify-content-start k-gap-2">
                                        <span *ngIf="groupeId" (click)="suggererPharmacie(dataItem?.id)"
                                            class="actions-icons  action-success  pointer-cus"
                                            title="Suggérer des modifications">
                                            <i class="bi bi-pencil-square"></i>
                                        </span>

                                        <span *ngIf="!readOnly && dataItem?.role !== 'member'" (click)="clearGrid()"
                                            class="actions-icons action-danger pointer-cus" title="Supprimer">
                                            <i class="bi bi-trash"></i>
                                        </span>

                                        <ng-container *jhiHasAnyAuthority="['ROLE_SUPER_ADMIN']">
                                            <span *ngIf="!readOnly && dataItem?.role === 'member'"
                                                (click)="detacherMembre(dataItem)"
                                                class="actions-icons action-danger pointer-cus" title="Détacher Membre">
                                                <i class="bi bi-trash"></i>
                                            </span>

                                            <span *ngIf="readOnly"
                                                (click)="openModalIdentifiants(modeEnvoieModal, dataItem)"
                                                title="Envoyer Identifiants" placement="left" tooltipClass="bobo"
                                                class="actions-icons pointer-cus bg-primary d-flex align-items-center justify-content-center k-gap-1">
                                                <i class="bi bi-envelope-arrow-up text-white"></i>
                                            </span>
                                        </ng-container>
                                    </div>
                                </ng-template>
                            </kendo-grid-column>

                            <kendo-grid-messages pagerItems="ligne(s)" pagerOf="de"
                                pagerItemsPerPage="éléments par page"></kendo-grid-messages>

                            <ng-template kendoGridNoRecordsTemplate>
                                <span>Veuillez effectuer une recherche</span>
                            </ng-template>
                        </kendo-grid>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<ng-template #indisponible>
    <span class="d-flex align-items-center justify-center k-gap-1 text-warning">
        <i class="bi bi-shield-fill-exclamation"></i>
        Indisponible
    </span>
</ng-template>

<ng-template #selectionnerChef let-modal>
    <div class="modal-header">
        <h4 class="modal-title text-dark" id="modal-basic-title">{{ (isMembreModal ? 'Ajouter un membre du groupe' :
            'Sélectionner le responsable du groupe') | uppercase }}</h4>
        <button type="button" class="close" tabindex="-1" aria-label="Close" (click)="modal.dismiss('Cross click')">
            <i class="mdi mdi-close"></i>
        </button>
    </div>

    <div class="modal-body">
        <form class="mb-2" [formGroup]="filterForm" (ngSubmit)="appliquerFiltre()">
            <div class="row flex-wrap">
                <div class="col-xl col-md-4  px-1">
                    <label for="nomResponsable" class="form-label">Nom Responsable</label>

                    <div class="input-group">
                        <input type="text" class="form-control form-control-md" id="nomResponsable"
                            formControlName="nomResponsable" />
                    </div>
                </div>

                <div class="col-xl col-md-4 px-1">
                    <label for="raisonSociale" class="form-label">Raison Sociale</label>

                    <div class="input-group">
                        <input type="text" class="form-control form-control-md" id="raisonSociale"
                            formControlName="raisonSociale" />
                    </div>
                </div>

                <div class="col-xl col-md-4  px-1">
                    <label for="ville" class="form-label">Ville</label>

                    <div class="input-group picker-input">
                        <input type="text" [readOnly]="readOnly" class="form-control form-control-md pl-4" id="ville"
                            formControlName="ville" [ngbTypeahead]="searchVilleOuLocalite"
                            [inputFormatter]="villeFormatter" [resultTemplate]="searchVilleTemplate" />

                        <div class="picker-icons picker-icons-alt"><i class="mdi mdi-magnify pointer"></i></div>

                        <ng-template #searchVilleTemplate let-result="result">
                            <b>{{ result?.labelFr }}</b>
                        </ng-template>
                    </div>
                </div>

                <div class="col-xl col-md-4 px-1">
                    <label for="localite" class="form-label">Localité</label>

                    <div class="input-group">
                        <input type="text" [readOnly]="readOnly" class="form-control form-control-md" id="localite"
                            formControlName="localite" />
                    </div>
                </div>

                <div class="col-xl col-md-4 mt-1 pt-0 pb-0 px-0">
                    <label class="col-sm-6 form-label p-0 ml-2" for="selectstatut"
                        style="margin-bottom: 0; margin-top: -2px">Statut</label>

                    <div class="input-group">
                        <select2 id="selectstatut" formControlName="statutMembreEntreprise" [data]="stautsLabelsValues"
                            hideSelectedItems="false" class="form-control-sm w-100" multiple="false"></select2>

                    </div>
                </div>

                <div
                    class="col-xl-auto col-md-4  px-1 d-flex align-items-end justify-content-center justify-content-md-start mt-3 m-mt-0">
                    <div class="row d-flex align-items-end justify-content-center py-0">
                        <button type="button" (click)="viderFiltre()" title="Vider"
                            class="btn btn-sm btn-outline-primary b-radius">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>

                        <button type="submit" title="Appliquer filtre" class="btn btn-sm btn-primary b-radius mx-1">
                            <i class="mdi mdi-filter"></i>
                        </button>
                    </div>

                </div>
            </div>
        </form>

        <div class="row p-1 table-container" *ngIf="filterResult && filterResult.length > 0; else noResults"
            scrollListener>
            <div (click)="isMembreModal ? selectMembre(result) : selectItem(result)" class="table-row w-100"
                *ngFor="let result of filterResult"
                [ngStyle]="{'background': result?.groupeEntreprise ? 'var(--fs-chef-bg)' : 
                currentPlateforme === 'WIN_GROUPE' ? 'var(--wf-primary-100)' : 'var(--fs-secondary-tint-2)'}">

                <span class="table-cell">
                    <i class="mdi mdi-account-circle mdi-18px"></i>
                    <span class="mx-1">Dr. {{ result?.nomResponsable }}</span>
                </span>

                <span class="table-cell">
                    <i class="bi bi-shop" style="font-size: 18px; margin-right: 3px;"></i>

                    <b>PH. {{ result?.raisonSociale }}</b>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-map-marker-radius mdi-18px "></i>
                    <span class="mx-1">{{ result?.ville }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-email mdi-18px"></i>
                    <span class="mx-1">{{ result?.email ?? 'Email indisponible' }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-phone mdi-18px"></i>
                    <span class="mx-1">{{ result?.gsm1 ?? 'GSM indisponible' }}</span>
                </span>

                <span class="table-cell">
                    <i class="mdi mdi-phone mdi-18px"></i>
                    <span class="mx-1">{{ result?.telephone ?? 'Tél indisponible' }}</span>
                </span>

                <span class="table-cell"
                    [ngStyle]="{'color': !result?.groupeEntreprise  ? 'var(--fs-success)' : 'black'}">
                    <i class="mdi mdi-account-multiple mdi-18px"></i>
                    <span class="mx-1">{{ result?.groupeEntreprise ? 'Déjà Membre: ' +
                        result?.groupeEntreprise?.raisonSociale :
                        "Disponible"
                        }}</span>
                </span>

            </div>
        </div>

        <ng-template #noResults>
            <div class="w-100 text-center py-4">
                <p *ngIf="searchTerm">Aucun résultat trouvé pour '{{ searchTerm }}'.</p>
                <p *ngIf="!searchTerm">Aucun résultat trouvé.</p>
                <button *ngIf="searchTerm" (click)="suggestPharmacyAndCloseModal()" class="btn btn-sm btn-primary"
                    title="Ajouter un produit" style="padding-block: 6px; border-radius: 20px;">
                    <i class="mdi mdi-assistant"></i>
                    Suggérer '{{ searchTerm }}' comme pharmacie
                </button>
            </div>
        </ng-template>
    </div>
</ng-template>


<ng-template #modeEnvoieModal let-modal>
    <div
        class="fs-modal bg-white d-flex align-items-center justify-content-center flex-column p-2 pt-3 position-relative">
        <div class="cross-button">
            <i class="bi bi-x pointer" (click)="modal.dismiss()"></i>
        </div>
        <div class="d-flex align-items-center justify-content-center k-gap-2">
            <p class="success-text text-warning text-center m-0">
                <img src="assets/icons/info-warning.png" height="40" width="40" alt=""> <br>
                <span>Envoyer Identifiants</span>
            </p>
        </div>
        <div class=" mt-2">
            <p class="message-text p-0 text-center">Veuillez sélectionner une méthode d'envoi des identifiants: </p>
        </div>

        <div class="row mb-1">
            <div class="form-group m-1 d-flex align-items-center">
                <input type="radio" id="whatsApp" class="form-control" [(ngModel)]="modeEnvoiIdentifiants"
                    name="identifiants" value="whatsapp" style="width: 25px !important; height: 25px !important;">
                <label for="whatsApp" class="form-label mx-1 mt-1">WhatsApp</label>
            </div>

            <div class="form-group m-1 d-flex align-items-center">
                <input type="radio" id="emailSms" class="form-control" [(ngModel)]="modeEnvoiIdentifiants"
                    name="identifiants" value="mail" style="width: 25px !important; height: 25px !important;"> <label
                    for="emailSms" class="form-label mx-1 mt-1">E-mail et SMS</label>
            </div>
        </div>

        <div class="actions d-flex w-100 justify-content-center align-items-center k-gap-2">
            <button (click)="sendCredentialsGeneric(); modal.dismiss('confirm click')"
                class="btn btn-fs-confirm  btn-block" tabindex="-1"
                [disabled]="!modeEnvoiIdentifiants">Confirmer</button>
            <button class="btn btn-block btn-fs-cancel m-0" tabindex="-1"
                (click)="modal.dismiss('cancel click')">Annuler</button>
        </div>
    </div>

</ng-template>