.cmd-inp-lg{
  font-size:14px !important;
  padding: 3px !important;
  background: #eeeeee !important;
  height: 20px !important;
  border-radius: 50px !important;
  margin-right:0 !important;
}
.label-sm{
  font-size: 16px;
}
.ion-list-item{
  // --min-height:25px !important;
  --padding-end: 0px;
  --inner-padding-end: 0px;
  ion-input{
    text-align: end;
    padding-right:10px !important;
  }
}
.title-wrapper{
  display:flex;
  justify-content: space-between;
  align-items: center;
  width:100%;
  margin-top: -7px;
  margin-bottom: -2px;
}
.item-column{
  background: #EDECF1;
  border-radius: 50px;
  text-align: center;
  padding: 5px 9px;
  font-size: 14px;
  min-width:100px;
}
.py-confirm{
  padding-top: 4px;
  padding-bottom: 4px;
}
.badge-option-lg{
  font-size: 13px !important;
  font-weight: 400 !important;
  width: 100px;
  text-align: center !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
.badge-option-xsm{
  font-size:12px !important;
  font-weight: 400 !important;
  margin-right:5px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.left-part{
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  align-items: start;
  .inner-part{
    margin-top:2px !important;
    text-align: center;
    ion-badge{
      margin-right:10px !important;
    }
  }
}
.footer-wrapper{
  background-color: #3DA5D9;
  min-height:50px;
}
