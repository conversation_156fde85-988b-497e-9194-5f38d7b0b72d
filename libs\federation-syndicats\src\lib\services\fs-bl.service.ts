import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Page, Pagination } from '@wph/data-access';
import { BLCriteria } from '../models/bl-criteria';
import { Observable } from 'rxjs';
import { DetailBlAchatGroupeDTO, EnteteBlConsolideeMarcheDTO, EnteteBlIndividuel, EnteteBlUnitaireMarcheDTO } from '../models/bl.model';

@Injectable({
  providedIn: 'root'
})


export class FsBLService {

  constructor(
    @Inject('ENVIROMENT') private env: any,
    private http:HttpClient) { }

  // Add your service methods here

  getPageNumber(skip: number, pageSize: number) {
    return (skip && pageSize) ? Math.floor(skip / pageSize) : 0;
}

buildNavigationParams(pagination: Pagination) {
    const params = {
        page: String(this.getPageNumber(pagination.skip, pagination.pageSize) ?? 0),
        size: String(pagination.pageSize) ?? 20,
    }

    if (pagination.sortField) {
        params['sort'] = pagination.sortField + ',' + pagination.sortMethod;
    }

    return params;
}



  searchBLUnitaire(pagination: Pagination, criteria:BLCriteria) {
    const params = this.buildNavigationParams(pagination);
    return this.http.post<Page<EnteteBlUnitaireMarcheDTO>>(`${this.env.base_url}/api/v1/bl-achat-groupe/search-bl-unitaire`,criteria,{params});
  }


  searchBLConsolide(pagination: Pagination, criteria:BLCriteria):Observable<Page<EnteteBlConsolideeMarcheDTO>> {
    const params = this.buildNavigationParams(pagination);
    return this.http.post<Page<EnteteBlConsolideeMarcheDTO>>(`${this.env.base_url}/api/v1/bl-achat-groupe/search-bl-consolide`,criteria,{params});

  }

  getMyBLsUnitaire(pagination: Pagination, criteria:BLCriteria):Observable<Page<EnteteBlUnitaireMarcheDTO>> {
    const params = this.buildNavigationParams(pagination);
    return this.http.post<Page<EnteteBlUnitaireMarcheDTO>>(`${this.env.base_url}/api/v1/bl-achat-groupe/my-bls-unitaire`,criteria,{params});

  }



  getBLsConsolideByCommandeId(validateBlParams:{cmdConsolideeId : string}) {
    return this.http.get<EnteteBlConsolideeMarcheDTO[]>(`${this.env.base_url}/api/v1/bl-achat-groupe/get-bl-consolide-by-cmd-id`,{params:validateBlParams});
  }

  getBLsUnitaireByCommandeId(validateBlParams:{cmdUnitaireId  : number}) {
    return this.http.get<EnteteBlUnitaireMarcheDTO[]>(`${this.env.base_url}/api/v1/bl-achat-groupe/get-bl-unitaire-by-cmd-id`,{params:validateBlParams});
  }


  getBLConsolideById(validateBlParams:{blConsolideId  : string, groupeId : number}) {
    return this.http.get<EnteteBlConsolideeMarcheDTO>(`${this.env.base_url}/api/v1/bl-achat-groupe/get-bl-consolide-by-id`,{params:validateBlParams});
  }


  getBLUnitairesByBLConsolideId(validateBlParams:{blConsolideId:string}) {
    return this.http.get<EnteteBlUnitaireMarcheDTO[]>(`${this.env.base_url}/api/v1/bl-achat-groupe/get-bl-unitaire-by-bl-consolide`,{params:validateBlParams});
  }




  getBLUnitaireById(validateBlParams:{blUnitaireId :string}) {
    return this.http.get<EnteteBlUnitaireMarcheDTO>(`${this.env.base_url}/api/v1/bl-achat-groupe/get-bl-unitaire-by-id`,{params:validateBlParams});
  }

  saveBlConsolide(BLCreationModel:EnteteBlConsolideeMarcheDTO,query:{groupeId :number}) {

    return this.http.post<EnteteBlConsolideeMarcheDTO>(`${this.env.base_url}/api/v1/bl-achat-groupe/save-bl-consolide`,BLCreationModel,{params:query});
  }


  validateBLConsolide(validateBlParams:{blConsolideId : number, groupeId  : number}) :Observable<null> {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-achat-groupe/valider-bl-consolide`,{params:validateBlParams});
  }

  dispatchBLConsolide(validateBlParams:{blConsolideId: string, groupeId : number,prioriserLeMoinsCommande : boolean}) {
    return this.http.post<EnteteBlUnitaireMarcheDTO[]>(`${this.env.base_url}/api/v1/bl-achat-groupe/dispatch-bl-consolide`,{},{params:validateBlParams});
  }


  anuulerBLConsolide(validateBlParams:{blConsolideId : string, groupeId : number}) {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-achat-groupe/annuler-bl-consolide`,{params:validateBlParams});
  }


  cloturerBLConsolide(validateBlParams:{blConsolideId : number, groupeId  : number}) :Observable<null> {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-achat-groupe/cloturer-bl-consolide`,{params:validateBlParams});
  }

  saveBlDispatcher(details:DetailBlAchatGroupeDTO[]) {
    return this.http.post<any>(`${this.env.base_url}/api/v1/bl-achat-groupe/save-details-bl-unitaire`,details);
  }

  validateBlDispatcher(params:{blConsolideId : string}) {
    return this.http.get<any>(`${this.env.base_url}/api/v1/bl-achat-groupe/valider-dispatch`,{params});
  }

  imprimerBLUnitaire(params:{blUnitaireId  : string}) {
    return this.http.get(`${this.env.base_url}/api/v1/bl-achat-groupe/print-bl-unitaire`,{params,responseType: 'blob' as 'json'});
  }

  envoyerBlsUnitaire(params:{blConsolideId  : string}) {
    return this.http.get(`${this.env.base_url}/api/v1/bl-achat-groupe/envoyer-bls-unitaire`,{params});
  }

  // ---------------------------------- Individuel BL ---------------------------------

  searchMyBLsIndividuel(pagination: Pagination, criteria:Partial<BLCriteria>):Observable<Page<EnteteBlIndividuel>> {
    const params = this.buildNavigationParams(pagination);
    return this.http.post<Page<EnteteBlIndividuel>>(`${this.env.base_url}/api/v1/bl-individuel/search-bl`,criteria,{params});

  }

  getBLsIndividuelByCommandeId(id:number) {
    return this.http.get<EnteteBlConsolideeMarcheDTO[]>(`${this.env.base_url}/api/v1/bl-individuel/get-bl-by-cmd-id`,{params:{cmdIndividuelleId :id}});
  }

  saveBlIndividuel(BLCreationModel:EnteteBlIndividuel) {

    return this.http.post<EnteteBlIndividuel>(`${this.env.base_url}/api/v1/bl-individuel/save-bl`,BLCreationModel);
  }

  validateBLIndividuel(validateBlParams:{blIndividuelId : number}) :Observable<null> {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-individuel/valider-bl`,{params:validateBlParams});
  }

  getBLIndividuelById(validateBlParams:{blIndividuelId :string}) {
    return this.http.get<EnteteBlIndividuel>(`${this.env.base_url}/api/v1/bl-individuel/get-bl-by-id`,{params:validateBlParams});
  }

  anuulerBLIndividuel(validateBlParams:{blIndividuelId : string}) {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-individuel/annuler-bl`,{params:validateBlParams});
  }


  deleteDispatch(params:{blConsolideId : string,groupeId : number}) {
    return this.http.get<null>(`${this.env.base_url}/api/v1/bl-achat-groupe/supprimer-blunit`,{params});
  }
}
