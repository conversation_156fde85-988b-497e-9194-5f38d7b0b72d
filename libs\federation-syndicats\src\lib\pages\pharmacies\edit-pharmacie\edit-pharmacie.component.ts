import { NgxMaskModule } from 'ngx-mask';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { <PERSON>arma<PERSON> } from '../../../models/pharmacie.model';
import { UserInputService } from '@wph/web/shared';
import { FederationSyndicatService } from '../../../services/federation-syndicats.service';
import {
  OperatorFunction,
  Observable,
  debounceTime,
  distinctUntilChanged,
  map,
  switchMap,
  of,
} from 'rxjs';
import { Alert, AlertService, ICity, StaticDataService } from '@wph/shared';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { phoneValidator } from '../../../validators/phone-validator';
import { AuthService } from '@wph/core/auth';
import { SuggestionFicheClient } from '../../../models/suggestion-fiche-client.model';
import { PharmacieEntreprise } from '../../../models/pharmacie-entreprise.model';
import { Localite } from 'libs/shared/src/lib/models/localite.model';

type PageMode = 'modifier' | 'ajouter';

@Component({
  selector: 'wph-edit-pharmacie',
  templateUrl: './edit-pharmacie.component.html',
  styleUrls: ['./edit-pharmacie.component.scss'],
})
export class EditPharmacieComponent implements OnInit {
  @ViewChild('successModalAlt') successModalAlt: any;

  model: Pharmacie;
  ville: any[];
  localites: Localite[];
  mode: PageMode = 'ajouter';
  activeId = 1;
  alert: Alert;
  showToast = true;

  showMap: boolean;

  submitted: boolean;
  isFormReady: boolean;
  editForm: FormGroup | null = null;

  isSuperAdmin = false;
  isReadOnly = false;
  suggestionHandled = false;
  etatSuggestion: string;
  



  stautsLabelsValues: any[] = [
    { label: 'SA', value: 'SA' },
    { label: 'SARL', value: 'SARL' },
    { label: 'PP', value: 'PP' },
    { label: 'SAS', value: 'SAS' },
    { label: 'GIE', value: 'GIE' },
    { label: 'SNC', value: 'SNC' },
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private userInputService: UserInputService,
    private alertService: AlertService,
    private staticDataService: StaticDataService,
    private fedSyndicatService: FederationSyndicatService,
    private modalService: NgbModal,
    private authService: AuthService
  ) {
    this.initForm();
  }

  get f() {
    return this.editForm.controls;
  }

  get isInactive$() {
    return this.fedSyndicatService.inactiveAccount$;
  }

  ngOnInit(): void {
    this.checkUserRole();
    this.route.params.subscribe((params) => {
      const pharmaId = params['id'];

      if (pharmaId) {
        this.mode = 'modifier';
        this.route.queryParams.subscribe((queryParams) => {
          const readOnly = queryParams['readOnly'] === 'true';
          if (queryParams['data']) {
            const data = JSON.parse(queryParams['data']);
            this.editForm.patchValue(data);
          } else {
            if (!queryParams['suggerer']) {
              this.fetchPharmacieEntrepriseById(+pharmaId);
            } else {
              this.fedSyndicatService.getPharmacieById(+pharmaId).subscribe(res => {
                this.initSuggererForm(res);
              });
            }
          }
          this.isReadOnly = readOnly;
          if (readOnly) {
            this.disableFormControls();
          }
        });
      } else {
        this.mode = 'ajouter';
        this.isReadOnly = false;
      }
    });
    this.getListeVilles();
    this.getListeLocalites();
  }


  initSuggererForm(pharmacie: PharmacieEntreprise) {   
    this.editForm.patchValue({
      id: null,
      raisonSociale: pharmacie?.raisonSociale,
      nomResponsable: pharmacie?.nomResponsable,
      clientCible:pharmacie,
      ville: pharmacie?.ville,
      localite: pharmacie?.localite,
      adresse: pharmacie?.adresse,
      adresse2: pharmacie?.adresse2,
      adresseAr1: pharmacie?.adresseAr,
      adresseAr2: pharmacie?.adresse2Ar,
      gsm1: pharmacie?.gsm1,
      email: pharmacie?.email,
      telephone: pharmacie?.telephone,
      statutJuridique: pharmacie?.statutJuridique,
      fax: pharmacie?.fax,
      siteweb: pharmacie?.siteWeb,
      longitude: pharmacie?.longitude,
      latitude: pharmacie?.latitude,
      whatsapp: pharmacie?.whatsapp,
    });

    this.editForm.patchValue({
      ville: this.ville?.find(
        (v) => v.labelFr === this.editForm?.value?.ville
      ),
    });


    
  }

  checkUserRole(): void {
    this.isSuperAdmin = this.authService.hasAnyAuthority(['ROLE_SUPER_ADMIN']);
    if (this.isSuperAdmin || this.isReadOnly) {
      this.disableFormControls();
    }
  }

  enableFormControls(): void {
    if (this.editForm && !this.isSuperAdmin) {
      for (const control in this.editForm.controls) {
        this.editForm.controls[control].enable();
      }
    }
  }

  disableFormControls(): void {
    if (this.editForm) {
      for (const control in this.editForm.controls) {
        this.editForm.controls[control].disable();
      }
    }
  }

  getListeVilles(): void {
    this.staticDataService.getListCitiesByCountryId(1).subscribe((res) => {
      this.ville = res;
      this.editForm.patchValue({
        ville: this.ville.find(
          (v) => v.labelFr === this.editForm?.value?.ville
        ),
      });
    });
  }

  getListeLocalites(): void {
    this.staticDataService.getListLocalitiesByCountryId().subscribe((res) => {
      this.localites = res;
      this.editForm.patchValue({
        localite: this.localites.find(
          (v) => v.localite === this.editForm?.value?.localite
        ),
      });
    });
  }

  fetchPharmacieEntrepriseById(id: number): void {
    this.fedSyndicatService.getPharmacieEntrepriseById(id).subscribe((res) => {
      if (res) {
        console.log('Fetched Data:', res);  
        this.editForm.patchValue({
          id: res.id,
          raisonSociale: res.raisonSociale,
          nomResponsable: res.nomResponsable,
          ville: res.ville,
          localite: res.localite,
          adresse: res.adresse,
          adresse2: res.adresse2,
          adresseAr1: res.adresseAr,
          adresseAr2: res.adresse2Ar,
          gsm1: res.gsm1,
          clientCible: res.clientCible,
          email: res.email,
          telephone: res.telephone,
          statutJuridique: res.statutJuridique,
          fax: res.fax,
          siteweb: res.siteWeb,
          longitude: res.longitude,
          latitude: res.latitude,
          whatsapp: res.whatsapp,
        });

        if (res.id === null || res.id === undefined) {
          console.error('ID is null or undefined');
        }  
  
        if (res.statutJuridique) {
          this.editForm.get('statutJuridique').setValue(res.statutJuridique);
        }
  
        if (res.ville) {
          this.editForm.patchValue({
            ville: this.ville.find(v => v.labelFr === res.ville),
          });
        }
  
        this.fedSyndicatService.getSuggestionById(id).subscribe((suggestion: SuggestionFicheClient) => {
          this.suggestionHandled = suggestion && (suggestion.etatSuggestion === 'V' || suggestion.etatSuggestion === 'R' || suggestion.etatSuggestion === 'A');
  
          if (this.suggestionHandled || suggestion.etatSuggestion === 'A') {
            this.isReadOnly = true;
            this.disableFormControls();
          } else {
            this.isReadOnly = false;
            this.enableFormControls();
          }
        });
      } else {
        console.error('Aucune donnée trouvée pour l\'ID fourni');
        this.alertService.error('Aucune donnée trouvée pour l\'ID fourni', 'MODAL');
      }
    }, (error) => {
      console.error('Erreur lors de la récupération des données', error);
      this.alertService.error('Erreur lors de la récupération des données', 'MODAL');
    });
  }

  indexChanged(index: number) {
    setTimeout(() => {
      this.showMap = index === 2;
      this.activeId = index;
    }, 200);
  }

  goNext() {
    this.activeId += 1;

    //  let this because of the map glitch
    setTimeout(() => {
      this.showMap = this.activeId === 2;
    }, 200);
  }
  goBack() {
    this.activeId -= 1;

    //  let this because of the map glitch
    setTimeout(() => {
      this.showMap = this.activeId === 2;
    }, 200);
  }

  initForm() {
    this.submitted = false;
    console.log(
      this.model,
      this.ville,

      this.ville?.find((v: any) => v.id == this.model?.villeRc)
    );

    this.editForm = this.fb.group({
      id: [null],
      raisonSociale: [
        this.model?.raisonSociale,
        [Validators.required, Validators.maxLength(80)],
      ],
      raisonSocialeAr: [this.model?.raisonSociale, [Validators.maxLength(80)]],
      ville: [
        this.ville?.find((v: any) => v.id == this.model?.villeRc),
        [Validators.required, Validators.maxLength(40)],
      ],
      localite: [
        this.localites?.find((v: any) => v.id == this.model?.localiteId),
        [],
      ],
      adresseAr1: [this.model?.adresseAr1, [Validators.maxLength(80)]],
      adresseAr2: [this.model?.adresseAr2, [Validators.maxLength(80)]],
      adresseAr3: [this.model?.adresseAr3, [Validators.maxLength(80)]],
      adresse: [
        this.model?.adresse,
        [Validators.required, Validators.maxLength(80)],
      ],
      adresse2: [this.model?.adresse2, [Validators.maxLength(40)]],
      adresse3: [this.model?.adresse3, [Validators.maxLength(40)]],
      nomResponsable: [
        this.model?.nomResponsable,
        [Validators.required, Validators.maxLength(40)],
      ],
      statutJuridique: [
        this.model?.statutJuridique,
        [Validators.maxLength(40)],
      ],
      commentaire: [this.model?.commentaire, [Validators.maxLength(40)]],
      fax: [this.model?.fax, [Validators.maxLength(40)]],
      telephone: [
        this.model?.telephone ? this.model?.telephone : '',
        [Validators.required, Validators.maxLength(40), phoneValidator()],
      ],
      gsm1: [
        this.model?.gsm1 ? this.model?.gsm1 : '',
        [Validators.required, Validators.maxLength(40), phoneValidator()],
      ],
      whatsapp: [
        this.model?.whatsapp ? this.model?.whatsapp : '',
        [Validators.maxLength(40), phoneValidator(true)],
      ],
      email: [
        this.model?.email,
        [
          Validators.required,
          Validators.email,
          Validators.maxLength(40),
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
          ),
        ],
      ],
      latitude: [this.model?.latitude, []],
      longitude: [this.model?.longitude, []],
      clientCible: [this.model?.clientCible],
      services: this.fb.array([]),
      horaires: this.fb.array([]),
      listeToutesGardes: this.fb.array([]),
    });

    this.isFormReady = true;
    this.checkUserRole();
  }

  confirmSaveChanges(): void {
    this.userInputService
      .confirmAlt('Confirmation', 'Êtes-vous sûr de vouloir enregistrer cette suggestion ?')
      .then((confirmed) => {
        if (confirmed) {
          this.saveChanges();
        }
      }, () => null
    );
  }

  saveChanges(): void {
    const { ville, localite, statutJuridique,...payload } = this.editForm.getRawValue();

    this.fedSyndicatService
      .saveFichePharmacieEntreprise({
        ...payload,
        ville: ville?.labelFr,
        localite: localite?.localite,
        statutJuridique: statutJuridique,
      })
      .subscribe({
        next: (res) => {
          if (this.mode === 'modifier') {
            this.alertService.successAlt(
              `La fiche pharmacie "${res?.raisonSociale}" a été modifiée avec succès.`,
              'Confirmation',
              'MODAL'
            );
            this.back();
          } else {
            this.alertService.successAlt(
              `La fiche pharmacie "${res?.raisonSociale}" a été enregistrée avec succès.`,
              'Confirmation',
              'MODAL'
            );
            setTimeout(() => {
              this.editForm.reset({ statutEntreprise: true });
              this.editForm.markAsPristine();
              this.editForm.markAsUntouched();
              this.submitted = false;
            }, 500);
          }
        },
        error: (err) => {
          const errorMessage =
            err?.error?.message ||
            "Une erreur est survenue lors de l'enregistrement. Veuillez réessayer.";
          this.alertService.error(errorMessage, 'MODAL');
          this.modalService.open(this.successModalAlt, {
            ariaLabelledBy: 'modal-basic-title',
            centered: true,
            windowClass: 'fs-modal-content',
          });
          console.error('Save error', err);
        },
      });
  }

  handleSaveChanges(): void {
    this.submitted = true;
    if (this.editForm.invalid) {
      this.alertService.error(
        'Les données du formulaire sont invalides ou incomplètes.',
        'TOAST'
      );
      return;
    }
    this.confirmSaveChanges();
}
  onSubmit(): void {
    this.submitted = true;
    if (this.editForm.valid) {
      const formValue = this.editForm.getRawValue();
      this.saveChanges();

      this.model.raisonSociale = formValue.raisonSociale
        ? formValue.raisonSociale
        : null;
      this.model.raisonSocialeAr = formValue.raisonSocialeAr
        ? formValue.raisonSocialeAr
        : null;
      this.model.adresse = formValue.adresse ? formValue.adresse : null;
      this.model.adresse2 = formValue.adresse2 ? formValue.adresse2 : '';
      this.model.adresse3 = formValue.adresse3 ? formValue.adresse3 : '';

      this.model.adresseAr1 = formValue.adresseAr1
        ? formValue.adresseAr1
        : null;
      this.model.adresseAr2 = formValue.adresseAr2 ? formValue.adresseAr2 : '';
      this.model.adresseAr3 = formValue.adresseAr3 ? formValue.adresseAr3 : '';
      this.model.commentaire = formValue.commentaire
        ? formValue.commentaire
        : null;

      this.model.telephone = formValue.telephone
        ? formValue?.telephone?.replace(/ +/g, '')
        : null;
      this.model.fax = formValue.fax ? formValue.fax : null;
      this.model.gsm1 = formValue.gsm1 ? formValue.gsm1 : null;

      this.model.latitude = formValue.latitude ? formValue.latitude : null;
      this.model.longitude = formValue.longitude ? formValue.longitude : null;

      this.model.siteweb = formValue.siteweb ? formValue.siteweb : null;
      this.model.statutJuridique = formValue.statutJuridique
        ? formValue.statutJuridique
        : null;
      this.model.ville = formValue.ville?.libelle || null;
      this.model.villeRc = formValue.ville?.id || null;

      this.model.localite = formValue.localite?.libelle
        ? formValue.localite.libelle
        : null;
      this.model.localiteId = formValue.localite?.id
        ? formValue.localite.id
        : null;

      this.model.nomResponsable = formValue.nomResponsable
        ? formValue.nomResponsable
        : null;
      this.model.whatsapp = formValue.whatsapp ? formValue.whatsapp : null;
      this.model.clientCible = formValue.clientCible ? formValue.clientCible : null;
      if (formValue?.services) {
        const temp = [];
        formValue.services.forEach((element) => {
          temp.push(element?.name);
        });
        this.model.services = temp;
      } else {
        this.model.services = [];
      }

      if (formValue?.horaires) {
        formValue?.horaires.forEach((horaire) => {
          let fullCrenaux = '';
          horaire.creneauxParsed.forEach((el) => {
            if (
              horaire.creneauxParsed[horaire.creneauxParsed.length - 1] === el
            ) {
              fullCrenaux += `${el.du}-${el.au}`;
            } else {
              fullCrenaux += `${el.du}-${el.au},`;
            }
          });

          horaire.creneaux = fullCrenaux;
          horaire.creneauxParsed = null;
          // return horaire;
        });
        this.model.horaires = formValue.horaires;
      } else {
        this.model.horaires = [];
      }

      this.submitChanges();
    } else {
      console.log('form not valid');
    }
  }

  confirmAction(action: 'valider' | 'rejeter'): void {
    const message = action === 'valider'
      ? 'Êtes-vous sûr de vouloir valider cette suggestion ?'
      : 'Êtes-vous sûr de vouloir rejeter cette suggestion ?';
  
    this.userInputService
      .confirmAlt('Confirmation', message)
      .then((confirmed) => {
        if (confirmed) {
          if (action === 'valider') {
            this.validerSuggestion();
          } else {
            this.rejeterSuggestion();
          }
        }
      })
      .catch((reason) => {
        if (reason === 'cancel' || reason === 'dismiss' || reason === 0 || reason === undefined) {
          // Handle user-initiated cancellation or dismissal
          console.log('Confirmation dialog was dismissed or canceled.');
        } else {
          // Log actual errors
          console.error('Confirmation dialog encountered an error:', reason);
        }
      });
  }
  
  

  validerSuggestion(): void {
    const suggestionId = this.editForm.get('id')?.value;
    console.log('Valider Suggestion ID:', suggestionId); // Log the ID for debugging
    if (suggestionId) {
      this.fedSyndicatService.validerSuggestion(suggestionId).subscribe({
        next: () => {
          this.alertService.successAlt('Suggestion validée avec succès!', undefined, 'MODAL');
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (err) => {
          const errorMessage = err?.error?.message || 'Une erreur est survenue lors de la validation. Veuillez réessayer.';
          this.alertService.error(errorMessage, 'MODAL');
          console.error('Validation error', err);
        },
      });
    } else {
      this.alertService.error('ID de suggestion invalide', 'MODAL');
    }
  }

  rejeterSuggestion(): void {
    const suggestionId = this.editForm.get('id')?.value;
    console.log('Rejeter Suggestion ID:', suggestionId); // Log the ID for debugging
    if (suggestionId) {
      this.fedSyndicatService.rejeterSuggestion(suggestionId).subscribe({
        next: () => {
          this.alertService.successAlt('Suggestion rejetée avec succès!', undefined, 'MODAL');
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (err) => {
          const errorMessage = err?.error?.message || 'Une erreur est survenue lors du rejet. Veuillez réessayer.';
          this.alertService.error(errorMessage, 'MODAL');
          console.error('Rejection error', err);
        },
      });
    } else {
      this.alertService.error('ID de suggestion invalide', 'MODAL');
    }
  }

  isFormValid(): boolean {
    const controls = this.editForm.controls;
    return (
      controls['raisonSociale'].valid &&
      controls['nomResponsable'].valid &&
      controls['adresse'].valid &&
      controls['email'].valid &&
      controls['telephone'].valid &&
      controls['gsm1'].valid &&
      (
        controls['raisonSociale'].touched || controls['raisonSociale'].dirty ||
        controls['nomResponsable'].touched || controls['nomResponsable'].dirty ||
        controls['adresse'].touched || controls['adresse'].dirty ||
        controls['email'].touched || controls['email'].dirty ||
        controls['telephone'].touched || controls['telephone'].dirty ||
        controls['gsm1'].touched || controls['gsm1'].dirty || this.submitted
      )
    );
  }

  isFormInvalid(): boolean {
    const controls = this.editForm.controls;
    return (
      (
        controls['raisonSociale'].invalid ||
        controls['nomResponsable'].invalid ||
        controls['adresse'].invalid ||
        controls['email'].invalid ||
        controls['telephone'].invalid ||
        controls['gsm1'].invalid
      ) &&
      (
        controls['raisonSociale'].touched || controls['raisonSociale'].dirty ||
        controls['nomResponsable'].touched || controls['nomResponsable'].dirty ||
        controls['adresse'].touched || controls['adresse'].dirty ||
        controls['email'].touched || controls['email'].dirty ||
        controls['telephone'].touched || controls['telephone'].dirty ||
        controls['gsm1'].touched || controls['gsm1'].dirty || this.submitted
      )
    );
  }

  isLocationFormValid(): boolean {
    const control = this.editForm.get('ville');
    return control.valid && (control.touched || control.dirty || this.submitted);  }

  isLocationFormInvalid(): boolean {
    const control = this.editForm.get('ville');
    return control.invalid && (control.touched || control.dirty || this.submitted);
  }


  onSelectedItem(_event: any) {
    this.submitted = false;
  }

  submitChanges(): void {
    this.fedSyndicatService.editPharmacie(this.model).subscribe((_data) => {
      //this.alertService.info("Pharmacie Saved");
      this.editForm.reset();
      this.isFormReady = false;
      //this.loadModels();
      this.submitted = false;
    });
  }

  desassocierPharmacie() {
    this.userInputService
      .confirm(null, 'êtes-vous sûr de vouloir appliquer cette action ?')
      .then(
        (_result) => {
          this.fedSyndicatService
            .desassocierPharmacie(this.model.idhash)
            .subscribe((_res) => {
              this.isFormReady = false;
              //this.loadModels();
            });
        },
        (_cancel) => {}
      );
  }

  setCoords(event: any) {
    const [latitude, longitude] = event;
    this.userInputService
      .confirmAlt(
        null,
        'êtes-vous sûr de vouloir changer les coordonnées de localisation ?',
      )
      .then(
        (_result) => {
          this.editForm.patchValue({
            longitude: longitude,
            latitude: latitude,
          });
        },
        (_cancel) => {}
      );
  }

  formatter = (result: ICity) => result?.labelFr;
  localiteFormatter = (result: Localite) => result?.localite;

  searchVille: OperatorFunction<string, readonly string[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      switchMap((term) => {
        return term?.length < 1
          ? of([])
          : of(
              this.ville?.filter((ville) =>
                ville?.labelFr?.toLowerCase()?.includes(term.toLowerCase())
              )
            );
      }),
      map((res) => res?.slice(0, 10))
    );

  searchLocalite: OperatorFunction<any, readonly any[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      map((term) =>
        term === ''
          ? []
          : this.localites
              .filter(
                (v) => v.localite.toLowerCase().indexOf(term.toLowerCase()) > -1
              )
              .slice(0, 10)
      )
    );

  back(): void {
    this.route.queryParams.subscribe(params => {
      if(params['from'] === 'param-memeber') {
        if (params['ref']) {
          this.router.navigate([`/achats-groupes/groupes/${params['ref']}/parametrer`],{
            queryParams: { readOnly:params['read'],}
          });
        } else {
          this.router.navigateByUrl('/achats-groupes/groupes/saisie');
        }
      }
      else if(params['from'] === 'mon-gr'){
        this.router.navigateByUrl('/achats-groupes/groupes/membres');
      }
      else if (params['from'] === 'param-memeber') {
        this.router.navigateByUrl('/achats-groupes/groupes/membres-saisie');
      }
      else{
        this.router.navigateByUrl('achats-groupes/pharmacies/liste');
      }
    })
  }
}
